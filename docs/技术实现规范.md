# 技术实现规范

## 🏗️ 项目架构概述

### 当前技术栈
- **前端框架：** React 18 + TypeScript
- **构建工具：** Vite
- **UI组件库：** Shadcn/ui + Radix UI
- **样式框架：** Tailwind CSS
- **路由管理：** React Router DOM
- **状态管理：** React Hooks (useState, useEffect)
- **包管理器：** pnpm

### 项目结构
```
src/
├── components/          # 可复用组件
│   ├── ui/             # Shadcn/ui基础组件
│   ├── layout/         # 布局组件
│   ├── font/           # 字体相关组件
│   └── common/         # 通用组件
├── pages/              # 页面组件
│   ├── fonts/          # 字体详情页
│   ├── guides/         # 使用指南页
│   └── tools/          # 专门工具页
├── lib/                # 工具库和数据
│   ├── font-data.ts    # 字体数据
│   ├── utils.ts        # 工具函数
│   └── analytics.ts    # 分析追踪
├── hooks/              # 自定义Hooks
├── types/              # TypeScript类型定义
└── utils/              # 工具函数
```

---

## 📄 页面组件开发规范

### 字体详情页组件结构

#### 1. 页面组件模板
```typescript
// src/pages/fonts/[category]/page.tsx
import React from 'react';
import { useParams } from 'react-router-dom';
import SEOHead from '@/components/SEOHead';
import FontGenerator from '@/components/font/FontGenerator';
import ContentSection from '@/components/font/ContentSection';
import RelatedFonts from '@/components/font/RelatedFonts';
import { getFontsByCategory } from '@/lib/font-data';
import { getPageContent } from '@/lib/content-data';

interface FontDetailPageProps {
  category: string;
}

const FontDetailPage: React.FC<FontDetailPageProps> = () => {
  const { category } = useParams<{ category: string }>();
  
  if (!category) {
    return <div>Category not found</div>;
  }

  const fonts = getFontsByCategory(category);
  const content = getPageContent(category);
  
  if (!fonts.length || !content) {
    return <div>Page not found</div>;
  }

  return (
    <div className="min-h-screen">
      <SEOHead 
        title={content.seo.title}
        description={content.seo.description}
        keywords={content.seo.keywords}
        structuredData={content.seo.structuredData}
      />
      
      <main className="container mx-auto px-4 py-8">
        {/* 页面标题区域 */}
        <header className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">
            {content.title}
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {content.description}
          </p>
        </header>

        {/* 字体生成器区域 */}
        <FontGenerator 
          fonts={fonts}
          category={category}
          className="mb-16"
        />

        {/* 内容区域 */}
        <ContentSection 
          content={content.sections}
          className="mb-16"
        />

        {/* 相关字体推荐 */}
        <RelatedFonts 
          currentCategory={category}
          className="mb-16"
        />
      </main>
    </div>
  );
};

export default FontDetailPage;
```

#### 2. 字体生成器组件
```typescript
// src/components/font/FontGenerator.tsx
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Copy, Check } from 'lucide-react';
import { FontStyle } from '@/lib/font-data';
import { useToast } from '@/hooks/use-toast';

interface FontGeneratorProps {
  fonts: FontStyle[];
  category: string;
  className?: string;
}

const FontGenerator: React.FC<FontGeneratorProps> = ({ 
  fonts, 
  category, 
  className 
}) => {
  const [inputText, setInputText] = useState('Hello World');
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});
  const { toast } = useToast();

  // 保存用户输入到localStorage
  useEffect(() => {
    const savedText = localStorage.getItem(`fontGenerator_${category}`);
    if (savedText) {
      setInputText(savedText);
    }
  }, [category]);

  useEffect(() => {
    localStorage.setItem(`fontGenerator_${category}`, inputText);
  }, [inputText, category]);

  const handleCopy = async (text: string, fontName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [fontName]: true }));
      
      toast({
        title: "复制成功！",
        description: `"${fontName}" 样式已复制到剪贴板`,
      });
      
      // 2秒后重置复制状态
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [fontName]: false }));
      }, 2000);
    } catch (error) {
      toast({
        title: "复制失败",
        description: "请手动选择文字进行复制",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className={`max-w-4xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="text-center">
          {category === 'cursive' ? '草书字体生成器' : 
           category === 'tattoo' ? '纹身字体生成器' : 
           '字体生成器'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 输入区域 */}
        <div className="space-y-2">
          <label htmlFor="text-input" className="text-sm font-medium">
            输入你的文字
          </label>
          <Input
            id="text-input"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="在这里输入文字..."
            className="text-lg"
          />
        </div>

        {/* 字体展示区域 */}
        <div className="space-y-4">
          {fonts.map((font) => {
            const transformedText = font.transform(inputText);
            const isCopied = copiedStates[font.name] || false;
            
            return (
              <div 
                key={font.name}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex-1">
                  <div className="text-sm font-medium text-muted-foreground mb-1">
                    {font.name}
                  </div>
                  <div className="text-xl break-all">
                    {transformedText}
                  </div>
                </div>
                <Button
                  size="sm"
                  variant={isCopied ? "default" : "outline"}
                  onClick={() => handleCopy(transformedText, font.name)}
                  className="ml-4"
                >
                  {isCopied ? (
                    <>
                      <Check className="h-4 w-4 mr-1" />
                      已复制
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </>
                  )}
                </Button>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default FontGenerator;
```

#### 3. 内容展示组件
```typescript
// src/components/font/ContentSection.tsx
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface ContentSectionProps {
  content: {
    introduction: string;
    whatIs: string;
    howToUse: string;
    applications: string;
    tips: string;
    faq: Array<{ question: string; answer: string }>;
  };
  className?: string;
}

const ContentSection: React.FC<ContentSectionProps> = ({ 
  content, 
  className 
}) => {
  return (
    <div className={`max-w-4xl mx-auto space-y-8 ${className}`}>
      {/* 介绍部分 */}
      <Card>
        <CardContent className="p-6">
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content.introduction }}
          />
        </CardContent>
      </Card>

      {/* 什么是该字体 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold mb-4">什么是这种字体？</h2>
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content.whatIs }}
          />
        </CardContent>
      </Card>

      {/* 使用指南 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold mb-4">如何使用？</h2>
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content.howToUse }}
          />
        </CardContent>
      </Card>

      {/* 应用场景 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold mb-4">应用场景</h2>
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content.applications }}
          />
        </CardContent>
      </Card>

      {/* 设计建议 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold mb-4">设计建议</h2>
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: content.tips }}
          />
        </CardContent>
      </Card>

      {/* 常见问题 */}
      <Card>
        <CardContent className="p-6">
          <h2 className="text-2xl font-bold mb-4">常见问题</h2>
          <div className="space-y-4">
            {content.faq.map((item, index) => (
              <div key={index} className="border-l-4 border-primary pl-4">
                <h3 className="font-semibold mb-2">{item.question}</h3>
                <p className="text-muted-foreground">{item.answer}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContentSection;
```

---

## 🗂️ 数据管理规范

### 字体数据扩展
```typescript
// src/lib/font-data.ts
export interface FontStyle {
  name: string;
  transform: (text: string) => string;
  category: "cursive" | "tattoo" | "other";
  tags: string[];
  description: string;
  popularity: number;
}

export const fontStyles: FontStyle[] = [
  {
    name: "Cursive",
    category: "cursive",
    tags: ["elegant", "handwriting", "script"],
    description: "优雅的草书字体，适合个人签名和装饰性文字",
    popularity: 95,
    transform: (text) => {
      // 转换逻辑
    }
  },
  // ... 其他字体
];

// 按分类获取字体
export const getFontsByCategory = (category: string): FontStyle[] => {
  return fontStyles.filter(font => 
    font.category === category || category === 'all'
  );
};

// 获取相关字体推荐
export const getRelatedFonts = (currentCategory: string, limit = 4): FontStyle[] => {
  return fontStyles
    .filter(font => font.category !== currentCategory)
    .sort((a, b) => b.popularity - a.popularity)
    .slice(0, limit);
};
```

### 内容数据管理
```typescript
// src/lib/content-data.ts
export interface PageContent {
  title: string;
  description: string;
  sections: {
    introduction: string;
    whatIs: string;
    howToUse: string;
    applications: string;
    tips: string;
    faq: Array<{ question: string; answer: string }>;
  };
  seo: {
    title: string;
    description: string;
    keywords: string[];
    structuredData: object;
  };
}

export const contentData: Record<string, PageContent> = {
  'cursive': {
    title: '免费草书字体生成器',
    description: '将普通文字转换为优雅的草书字体，支持一键复制粘贴',
    sections: {
      introduction: '草书字体介绍内容...',
      whatIs: '什么是草书字体...',
      howToUse: '使用方法...',
      applications: '应用场景...',
      tips: '设计建议...',
      faq: [
        {
          question: '草书字体可以商用吗？',
          answer: '基于Unicode的字体可以自由使用...'
        }
      ]
    },
    seo: {
      title: '免费草书字体生成器 - 优雅手写体在线转换工具',
      description: '专业的草书字体生成器，支持多种优雅手写体样式，一键复制粘贴，适用于社交媒体、设计项目等场景。',
      keywords: ['草书字体', '字体生成器', '手写体', '优雅字体'],
      structuredData: {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "草书字体生成器"
      }
    }
  },
  // ... 其他页面内容
};

export const getPageContent = (category: string): PageContent | null => {
  return contentData[category] || null;
};
```

---

## 🔧 SEO组件规范

### SEO Head组件
```typescript
// src/components/SEOHead.tsx
import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title: string;
  description: string;
  keywords?: string[];
  structuredData?: object;
  canonical?: string;
  ogImage?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords = [],
  structuredData,
  canonical,
  ogImage = '/og-image.png'
}) => {
  const fullTitle = title.includes('Cursive Font Generator') 
    ? title 
    : `${title} | Cursive Font Generator`;

  return (
    <Helmet>
      {/* 基础SEO标签 */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Open Graph标签 */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:type" content="website" />
      
      {/* Twitter Card标签 */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* 结构化数据 */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;
```

---

## 🚀 路由配置规范

### 路由结构
```typescript
// src/App.tsx
import { BrowserRouter, Routes, Route } from "react-router-dom";
import HomePage from "@/pages/HomePage";
import FontDetailPage from "@/pages/fonts/FontDetailPage";
import GuideDetailPage from "@/pages/guides/GuideDetailPage";
import ToolDetailPage from "@/pages/tools/ToolDetailPage";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* 主页 */}
        <Route path="/" element={<HomePage />} />
        
        {/* 字体详情页 */}
        <Route path="/cursive-fonts" element={<FontDetailPage category="cursive" />} />
        <Route path="/tattoo-fonts" element={<FontDetailPage category="tattoo" />} />
        <Route path="/gothic-fonts" element={<FontDetailPage category="gothic" />} />
        {/* ... 其他字体页面 */}
        
        {/* 使用指南页 */}
        <Route path="/how-to-use-cursive-fonts-instagram" element={<GuideDetailPage guide="instagram" />} />
        <Route path="/tattoo-font-design-guide" element={<GuideDetailPage guide="tattoo-design" />} />
        {/* ... 其他指南页面 */}
        
        {/* 专门工具页 */}
        <Route path="/name-tattoo-generator" element={<ToolDetailPage tool="name-tattoo" />} />
        <Route path="/quote-font-generator" element={<ToolDetailPage tool="quote" />} />
        {/* ... 其他工具页面 */}
        
        {/* 现有页面 */}
        <Route path="/about" element={<AboutPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/privacy" element={<PrivacyPage />} />
        <Route path="/terms" element={<TermsPage />} />
        
        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  );
}
```

---

## 📊 性能优化规范

### 代码分割
```typescript
// 懒加载非关键组件
const FeaturesSection = React.lazy(() => import('@/components/FeaturesSection'));
const WhatIsSection = React.lazy(() => import('@/components/WhatIsSection'));
const SymbolLibrary = React.lazy(() => import('@/components/SymbolLibrary'));

// 使用Suspense包装
<Suspense fallback={<LoadingSpinner />}>
  <FeaturesSection />
</Suspense>
```

### 图片优化
```typescript
// 图片组件
const OptimizedImage: React.FC<{
  src: string;
  alt: string;
  width?: number;
  height?: number;
}> = ({ src, alt, width, height }) => {
  return (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      loading="lazy"
      decoding="async"
      className="max-w-full h-auto"
    />
  );
};
```

### 缓存策略
```typescript
// localStorage缓存用户输入
const useLocalStorage = (key: string, initialValue: string) => {
  const [value, setValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });

  const setStoredValue = (value: string) => {
    try {
      setValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  return [value, setStoredValue];
};
```

---

## 🧪 测试规范

### 组件测试
```typescript
// src/components/__tests__/FontGenerator.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import FontGenerator from '../font/FontGenerator';
import { mockFonts } from '@/lib/__mocks__/font-data';

describe('FontGenerator', () => {
  test('renders input field and font options', () => {
    render(<FontGenerator fonts={mockFonts} category="cursive" />);
    
    expect(screen.getByLabelText(/输入你的文字/i)).toBeInTheDocument();
    expect(screen.getByText('Cursive')).toBeInTheDocument();
  });

  test('transforms text when input changes', () => {
    render(<FontGenerator fonts={mockFonts} category="cursive" />);
    
    const input = screen.getByLabelText(/输入你的文字/i);
    fireEvent.change(input, { target: { value: 'Test' } });
    
    expect(screen.getByText(/𝒯𝑒𝓈𝓉/)).toBeInTheDocument();
  });
});
```

### E2E测试
```typescript
// cypress/e2e/font-generator.cy.ts
describe('Font Generator', () => {
  it('should generate and copy fonts', () => {
    cy.visit('/cursive-fonts');
    
    // 输入文字
    cy.get('[data-testid="text-input"]').type('Hello World');
    
    // 点击复制按钮
    cy.get('[data-testid="copy-button"]').first().click();
    
    // 验证复制成功提示
    cy.contains('复制成功').should('be.visible');
  });
});
```

---

**文档版本：** v1.0  
**创建时间：** 2025-06-24  
**适用范围：** 所有技术开发任务  
**维护责任：** 技术团队
