# SEO Scripts Guide

This document explains how to use the SEO-related scripts for sitemap management and search engine indexing.

## Available Scripts

### 1. Sitemap Management

#### `npm run update-sitemap`
Updates the sitemap.xml file with current URLs and automatically submits to Bing IndexNow.

```bash
# Update sitemap and submit to IndexNow
npm run update-sitemap

# Update sitemap only (skip IndexNow submission)
npm run update-sitemap -- --skip-indexnow
```

#### `npm run check-sitemap`
Checks all URLs in sitemap.xml to ensure they are accessible (no 404 errors).

```bash
# Check all URLs in sitemap
npm run check-sitemap
```

**Features:**
- Parallel URL checking with concurrency control
- Detailed console output with color coding
- Generates JSON report (`sitemap-check-report.json`)
- Exits with error code if any URLs fail
- <PERSON><PERSON> redirects and timeouts gracefully

### 2. Bing IndexNow Integration

#### `npm run bing-indexnow`
Submits all sitemap URLs to <PERSON>'s IndexNow API for faster indexing.

```bash
# Submit all sitemap URLs
npm run bing-indexnow

# Submit specific URLs
npm run bing-indexnow -- --urls=https://cursivefontgenerator.top,https://cursivefontgenerator.top/about

# Skip key file verification
npm run bing-indexnow-verify
```

**Features:**
- Automatic IndexNow key generation
- Key file accessibility verification
- Batch processing for large URL lists
- Retry mechanism with exponential backoff
- Submission logging (`indexnow-submissions.json`)

### 3. Combined SEO Check

#### `npm run seo-check`
Runs both sitemap checking and IndexNow submission in sequence.

```bash
# Full SEO check: verify URLs + submit to IndexNow
npm run seo-check
```

## IndexNow Setup

### First Time Setup

1. **Generate IndexNow Key**: The script automatically generates a key on first run
2. **Deploy Key File**: Ensure `public/indexnow-key.txt` is accessible at your domain
3. **Verify Setup**: The script will verify key accessibility before submission

### Key File Requirements

The IndexNow key file must be accessible at:
```
https://cursivefontgenerator.top/indexnow-key.txt
```

The file contains a 32-character alphanumeric key that identifies your site to IndexNow.

### Manual Key Management

```bash
# Generate new key (if needed)
node scripts/bing-indexnow.js

# Submit with existing key
node scripts/bing-indexnow.js --skip-verification
```

## Script Options

### check-sitemap.js Options

```bash
node scripts/check-sitemap.js [options]

Options:
  --help, -h     Show help message
```

**Output Files:**
- `sitemap-check-report.json` - Detailed check results

### bing-indexnow.js Options

```bash
node scripts/bing-indexnow.js [options]

Options:
  --help, -h              Show help message
  --skip-verification     Skip key file accessibility check
  --urls=url1,url2,...    Submit specific URLs instead of sitemap URLs
```

**Output Files:**
- `public/indexnow-key.txt` - IndexNow authentication key
- `indexnow-submissions.json` - Submission history log

### update-sitemap.js Options

```bash
node scripts/update-sitemap.js [options]

Options:
  --skip-indexnow    Skip automatic IndexNow submission
```

## Integration with Build Process

The sitemap is automatically updated during the build process:

```json
{
  "scripts": {
    "prebuild": "npm run update-sitemap"
  }
}
```

This ensures the sitemap is always current when deploying.

## Monitoring and Logs

### Check Reports
- **Location**: `sitemap-check-report.json`
- **Content**: URL status, response times, error details
- **Retention**: Overwrites on each run

### IndexNow Logs
- **Location**: `indexnow-submissions.json`
- **Content**: Submission history, timestamps, results
- **Retention**: Last 50 submissions

### Example Log Entry
```json
{
  "timestamp": "2025-06-25T10:30:00.000Z",
  "host": "cursivefontgenerator.top",
  "key": "abc123...",
  "urlCount": 50,
  "results": [{"success": true, "status": 200}],
  "urls": ["https://cursivefontgenerator.top", "..."]
}
```

## Best Practices

### 1. Regular Checks
Run sitemap checks before major deployments:
```bash
npm run check-sitemap
```

### 2. Content Updates
Submit to IndexNow after significant content changes:
```bash
npm run bing-indexnow -- --urls=https://cursivefontgenerator.top/new-page
```

### 3. Automated Workflow
Use in CI/CD pipelines:
```bash
# In deployment script
npm run update-sitemap
npm run check-sitemap
```

### 4. Error Handling
Monitor exit codes for automation:
- `0` = Success
- `1` = Errors found (check-sitemap) or submission failed (bing-indexnow)

## Troubleshooting

### Common Issues

#### 1. Key File Not Accessible
```
❌ Key file not accessible: fetch failed
```
**Solution**: Ensure `public/indexnow-key.txt` is deployed and accessible

#### 2. URL Check Failures
```
❌ 404 - https://example.com/page
```
**Solution**: Fix broken links or remove from sitemap

#### 3. IndexNow Submission Failed
```
❌ IndexNow submission failed: HTTP 400
```
**Solution**: Check key validity and URL format

### Debug Mode
Add verbose logging by modifying scripts or checking log files.

## Security Considerations

1. **IndexNow Key**: Keep the key file secure but publicly accessible
2. **Rate Limiting**: Scripts include built-in rate limiting
3. **Error Handling**: Sensitive information is not logged

## Performance

- **Sitemap Check**: ~5 URLs per second (configurable)
- **IndexNow**: Batch submissions up to 10,000 URLs
- **Memory Usage**: Minimal, suitable for CI/CD environments
