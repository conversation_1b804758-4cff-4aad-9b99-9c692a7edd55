import React from 'react';
import { CategoryKey } from '@/config/font-categories';

interface FontCategoryFAQProps {
  categoryKey: CategoryKey;
}

const FontCategoryFAQ: React.FC<FontCategoryFAQProps> = ({ categoryKey }) => {
  const getFAQs = (category: CategoryKey) => {
    const faqMap: Record<CategoryKey, Array<{ question: string; answer: string }>> = {
      'cursive-fonts': [
        {
          question: "What's the difference between cursive and script fonts?",
          answer: "While often used interchangeably, cursive fonts specifically refer to connected, flowing letterforms that mimic handwriting, while script fonts encompass a broader category including both connected and disconnected decorative fonts."
        },
        {
          question: "Can I use cursive fonts for body text?",
          answer: "Cursive fonts are best reserved for headlines, quotes, and short text blocks. Extended reading in cursive fonts can cause eye strain due to their decorative nature and reduced legibility."
        },
        {
          question: "Do cursive fonts work well on mobile devices?",
          answer: "Yes, our Unicode cursive fonts are optimized for all devices. However, ensure adequate size (minimum 16px) and sufficient contrast for optimal mobile readability."
        },
        {
          question: "How do I pair cursive fonts with other typefaces?",
          answer: "Pair cursive fonts with clean, simple sans-serif or serif fonts for body text. Avoid mixing multiple decorative fonts, and ensure the pairing maintains good readability and visual hierarchy."
        },
        {
          question: "Are these cursive fonts suitable for wedding invitations?",
          answer: "Absolutely! Our elegant cursive fonts are perfect for wedding invitations, save-the-dates, and other formal stationery. They provide the romantic, sophisticated look that's ideal for special occasions."
        },
        {
          question: "Can I modify the letter spacing in cursive fonts?",
          answer: "Yes, you can adjust letter spacing (tracking) to improve readability or achieve a specific aesthetic. However, be careful not to break the natural flow that makes cursive fonts appealing."
        }
      ],
      'gothic-fonts': [
        {
          question: "Are Gothic fonts difficult to read?",
          answer: "Gothic fonts can be challenging to read in large blocks of text due to their dense, angular letterforms. They're best used for headlines, logos, and short text where dramatic impact is desired."
        },
        {
          question: "What's the historical significance of Gothic fonts?",
          answer: "Gothic fonts originated in 12th-century Europe and were the primary script for medieval manuscripts and early printed books, including the Gutenberg Bible. They represent over 400 years of typographic tradition."
        },
        {
          question: "Can Gothic fonts be used in modern branding?",
          answer: "Yes, Gothic fonts are excellent for brands wanting to convey tradition, craftsmanship, or authority. They're popular in industries like brewing, luxury goods, and entertainment (especially metal music and horror genres)."
        },
        {
          question: "Are there different styles of Gothic fonts?",
          answer: "Yes, Gothic fonts include several variations: Textura (dense, formal), Rotunda (rounder, Italian), Bastarda (French hybrid), and Fraktur (German style). Each has distinct characteristics and cultural associations."
        },
        {
          question: "How do I ensure Gothic fonts remain legible?",
          answer: "Use Gothic fonts at larger sizes (14pt+), provide ample line spacing, avoid all-caps for extended text, and ensure high contrast with the background. Consider the viewing distance and context."
        }
      ],
      'tattoo-fonts': [
        {
          question: "What makes a font suitable for tattoos?",
          answer: "Tattoo fonts need bold, clear lines that won't blur over time, adequate spacing to prevent ink bleeding, and simplified details that translate well to skin. They must remain legible as the tattoo ages."
        },
        {
          question: "How do I choose the right size for tattoo lettering?",
          answer: "Tattoo lettering should be at least 1/4 inch tall for small text and larger for script fonts. Consider the body placement, viewing distance, and how the skin stretches in that area."
        },
        {
          question: "Can I use any font for a tattoo?",
          answer: "Not all fonts work well for tattoos. Thin, delicate fonts may not age well, and overly complex fonts can lose detail. It's best to use fonts specifically designed for tattoo applications or consult with a professional tattoo artist."
        },
        {
          question: "Do tattoo fonts look different on different skin tones?",
          answer: "Yes, ink contrast varies with skin tone. Darker skin may require bolder lines and higher contrast, while lighter skin can accommodate more delicate details. A professional tattoo artist can advise on the best approach."
        },
        {
          question: "How long do script tattoos typically take?",
          answer: "Script tattoos vary widely in time depending on size, complexity, and placement. Simple text might take 1-2 hours, while elaborate script pieces can take multiple sessions of 4-6 hours each."
        }
      ],
      'bold-fonts': [
        {
          question: "When should I use bold fonts?",
          answer: "Use bold fonts for headlines, call-to-action buttons, important warnings, navigation elements, and any text that needs immediate attention. They're essential for creating visual hierarchy."
        },
        {
          question: "Can bold fonts improve accessibility?",
          answer: "Yes, bold fonts can improve readability for users with visual impairments by increasing contrast and making text more distinguishable. However, they should be used strategically, not for entire documents."
        },
        {
          question: "Do bold fonts affect website loading speed?",
          answer: "Bold font files are typically 15-30% larger than regular weights, which can slightly impact loading times. Optimize by loading only necessary weights and using font-display: swap for better performance."
        },
        {
          question: "How do bold fonts perform in print vs. digital?",
          answer: "Bold fonts may appear heavier in print than on screen due to ink spread. Test print samples and consider slightly reducing weight for print applications while maintaining digital optimization."
        },
        {
          question: "Can I use bold fonts for body text?",
          answer: "Bold fonts are generally not recommended for large blocks of body text as they can cause reading fatigue. Reserve bold for emphasis, headings, and short, important passages."
        }
      ],
      'italic-fonts': [
        {
          question: "What's the proper way to use italic fonts?",
          answer: "Use italics for book titles, foreign words, scientific names, emphasis within text, thoughts in fiction, and mathematical variables. Follow established style guides like Chicago Manual of Style or APA."
        },
        {
          question: "Are italic fonts harder to read than regular fonts?",
          answer: "Italic fonts can be slightly more challenging to read in large quantities due to their slanted nature. They're best used for emphasis and specific applications rather than extended body text."
        },
        {
          question: "What's the difference between italic and oblique fonts?",
          answer: "True italic fonts are completely redrawn with unique letterforms, while oblique fonts are simply slanted versions of regular fonts. Italics typically have more character and better readability."
        },
        {
          question: "Can I use italics for quotes?",
          answer: "Yes, italics are commonly used for block quotes, testimonials, and emphasized quotations. They help distinguish quoted material from regular text while maintaining readability."
        },
        {
          question: "How do italic fonts affect line spacing?",
          answer: "Italic fonts often benefit from slightly increased line spacing (5-10% more) to prevent the slanted letters from appearing cramped and to maintain optimal readability."
        }
      ],
      'small-caps-fonts': [
        {
          question: "When should I use small caps instead of regular capitals?",
          answer: "Use small caps for acronyms within text, author names in bibliographies, the first few words after a drop cap, and anywhere you need emphasis without the visual disruption of full capitals."
        },
        {
          question: "Are small caps considered more professional?",
          answer: "Yes, small caps are associated with high-quality typography and are standard in academic, legal, and corporate documents. They demonstrate attention to typographic detail and sophistication."
        },
        {
          question: "Can I create small caps by just making capitals smaller?",
          answer: "No, true small caps are specially designed letterforms with adjusted proportions and stroke weights. Simply scaling down capitals creates 'fake' small caps that look thin and poorly balanced."
        },
        {
          question: "Do small caps work in all languages?",
          answer: "Small caps work best in Latin-based alphabets. Some languages and writing systems don't have small cap variants, and the concept may not apply to scripts like Arabic or Chinese."
        },
        {
          question: "How do I access small caps in different software?",
          answer: "In professional design software, use OpenType features to access true small caps. In word processors, look for small caps formatting options, though these may create fake small caps rather than true ones."
        }
      ],
      'vaporwave-fonts': [
        {
          question: "What makes a font 'vaporwave'?",
          answer: "Vaporwave fonts typically feature wide letter spacing, geometric forms, retro-futuristic aesthetics, and often incorporate glitch effects or pixelation that evokes 1980s computer graphics and VHS aesthetics."
        },
        {
          question: "Can vaporwave fonts be used for serious business applications?",
          answer: "Vaporwave fonts are generally too stylized for traditional business use, but they can be effective for creative industries, tech startups, entertainment brands, and any business targeting millennial/Gen Z audiences with retro appeal."
        },
        {
          question: "What colors work best with vaporwave fonts?",
          answer: "Classic vaporwave colors include neon pink, cyan, purple gradients, and high-contrast combinations. These colors are essential to achieving the authentic vaporwave aesthetic and cultural references."
        },
        {
          question: "Are vaporwave fonts a passing trend?",
          answer: "While vaporwave peaked in the mid-2010s, its aesthetic influence continues in synthwave, retrowave, and cyberpunk design. The nostalgic appeal ensures continued relevance in specific contexts and subcultures."
        },
        {
          question: "How do I create authentic vaporwave typography?",
          answer: "Combine vaporwave fonts with period-appropriate design elements: grid patterns, geometric shapes, glitch effects, and classic vaporwave imagery like palm trees, sunsets, and classical sculptures."
        }
      ],
      'elegant-script-fonts': [
        {
          question: "What occasions are best for elegant script fonts?",
          answer: "Elegant script fonts are perfect for wedding invitations, formal announcements, luxury brand materials, certificates, high-end product packaging, and any design requiring sophistication and refinement."
        },
        {
          question: "How do I maintain readability with elegant scripts?",
          answer: "Use elegant scripts sparingly, ensure adequate size and spacing, provide high contrast with backgrounds, and always include readable alternatives for essential information."
        },
        {
          question: "Can elegant script fonts work in digital applications?",
          answer: "Yes, but use them carefully in digital contexts. They work well for headers, logos, and accent text, but avoid using them for navigation, forms, or any interactive elements where clarity is crucial."
        }
      ],
      'handwriting-fonts': [
        {
          question: "Do handwriting fonts look authentic?",
          answer: "Quality handwriting fonts can look very authentic, especially when they include natural variations and imperfections. The best handwriting fonts capture the organic flow and personality of real handwriting."
        },
        {
          question: "Are handwriting fonts appropriate for business use?",
          answer: "Handwriting fonts can work for businesses wanting to convey personal touch, creativity, or approachability. They're popular in industries like crafts, food, childcare, and personal services."
        },
        {
          question: "How do I choose between different handwriting styles?",
          answer: "Consider your audience and message: neat handwriting for professional contexts, casual scripts for friendly brands, and artistic styles for creative applications. Match the handwriting personality to your brand voice."
        }
      ],
      'calligraphy-fonts': [
        {
          question: "What's the difference between calligraphy and regular script fonts?",
          answer: "Calligraphy fonts are specifically designed to mimic traditional pen-and-ink calligraphy, featuring brush-like strokes, varying line weights, and artistic flourishes that reflect centuries-old writing traditions."
        },
        {
          question: "Can I use calligraphy fonts for modern designs?",
          answer: "Absolutely! Modern calligraphy fonts blend traditional techniques with contemporary aesthetics, making them perfect for wedding stationery, luxury branding, artistic projects, and sophisticated design applications."
        },
        {
          question: "Are calligraphy fonts difficult to read?",
          answer: "Calligraphy fonts prioritize artistic beauty over pure legibility, so they're best used for decorative purposes, headlines, and short text blocks rather than extensive reading material."
        }
      ],
      'old-english-fonts': [
        {
          question: "Are Old English fonts appropriate for modern use?",
          answer: "Old English fonts remain highly appropriate for formal documents, academic institutions, legal firms, and any context requiring traditional authority and gravitas. They're timeless rather than outdated."
        },
        {
          question: "How do I use Old English fonts without appearing outdated?",
          answer: "Pair Old English fonts with clean, modern typography for body text, use them selectively for headers and emphasis, and ensure the overall design balances tradition with contemporary elements."
        },
        {
          question: "What's the cultural significance of Old English fonts?",
          answer: "Old English fonts carry associations with academic excellence, legal authority, religious tradition, and institutional credibility. They're deeply embedded in Western cultural concepts of formality and importance."
        }
      ],
      'tattoo-script-fonts': [
        {
          question: "How do tattoo script fonts differ from regular script fonts?",
          answer: "Tattoo script fonts are specifically engineered for skin application, with enhanced stroke weights, optimized spacing for ink spread, and simplified details that maintain legibility as tattoos age over time."
        },
        {
          question: "Can tattoo script fonts be used for other design projects?",
          answer: "Yes! Tattoo script fonts work excellently for apparel design, logo creation, packaging for artisanal products, and any project requiring bold, authentic lettering with street credibility."
        },
        {
          question: "What should I consider when designing with tattoo script fonts?",
          answer: "Consider line weight, spacing, contrast, and the final application. Even for non-tattoo uses, these fonts work best with bold, high-contrast applications that respect their heritage and design principles."
        }
      ]
    };

    return faqMap[category] || [];
  };

  const faqs = getFAQs(categoryKey);

  if (faqs.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {faqs.map((faq, index) => (
        <div key={index}>
          <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
          <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
        </div>
      ))}
    </div>
  );
};

export default FontCategoryFAQ;
