import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON> } from "lucide-react"

const FeaturesSection = () => {
  return (
    <section className="py-16 md:py-24 relative overflow-hidden" id="features">
      {/* Background decoration */}
      <div className="absolute top-1/4 -left-20 w-40 h-40 bg-purple-600/10 rounded-full blur-2xl"></div>
      <div className="absolute bottom-1/4 right-0 w-60 h-60 bg-indigo-600/10 rounded-full blur-2xl"></div>
      
      <div className="container px-4 md:px-6 relative">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold gradient-text mb-4">
            Powerful Features
          </h2>
          <p className="text-xl opacity-90 max-w-3xl mx-auto">
            Everything you need to create amazing text styles and find perfect symbols
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Feature 1 */}
          <Card className="bg-background/70 backdrop-blur-sm transform hover:translate-y-[-5px] transition-all duration-300 overflow-hidden border-primary/10 shadow-lg">
            <CardContent className="p-0">
              <div className="h-1 bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              <div className="p-6">
                <div className="h-14 w-14 rounded-2xl bg-primary/10 flex items-center justify-center mb-5">
                  <svg className="h-7 w-7 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">100+ Cursive & Script Styles</h3>
                <p className="text-muted-foreground mb-5">
                  Transform ordinary text into <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">elegant scripts</a>, handwriting, tattoo, and many more unique styles with just a click.
                  Try our <a href="/elegant-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">premium collection</a> for sophisticated designs.
                </p>
                <ul className="space-y-2 mb-5">
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Instant font transformation</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>One-click copy to clipboard</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Works on all platforms</span>
                  </li>
                </ul>
                <Button variant="ghost" size="sm" className="gap-1" onClick={() => document.getElementById('font-generator')?.scrollIntoView({ behavior: 'smooth' })}>
                  <span>Try it now</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Feature 2 */}
          <Card className="bg-background/70 backdrop-blur-sm transform hover:translate-y-[-5px] transition-all duration-300 overflow-hidden border-primary/10 shadow-lg">
            <CardContent className="p-0">
              <div className="h-1 bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              <div className="p-6">
                <div className="h-14 w-14 rounded-2xl bg-primary/10 flex items-center justify-center mb-5">
                  <svg className="h-7 w-7 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Preview Your Cursive Signature</h3>
                <p className="text-muted-foreground mb-5">
                  See exactly how your new <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">fonts</a> or tattoo-style text will appear on social media platforms before you post it.
                  Perfect for testing <a href="/modern-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">modern styles</a>.
                </p>
                <ul className="space-y-2 mb-5">
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Instagram post mockups</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Twitter/X profile preview</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Facebook post simulation</span>
                  </li>
                </ul>
                <Button variant="ghost" size="sm" className="gap-1" onClick={() => document.getElementById('preview-section')?.scrollIntoView({ behavior: 'smooth' })}>
                  <span>Try it now</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Feature 3 */}
          <Card className="bg-background/70 backdrop-blur-sm transform hover:translate-y-[-5px] transition-all duration-300 overflow-hidden border-primary/10 shadow-lg">
            <CardContent className="p-0">
              <div className="h-1 bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              <div className="p-6">
                <div className="h-14 w-14 rounded-2xl bg-primary/10 flex items-center justify-center mb-5">
                  <svg className="h-7 w-7 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Cursive-Friendly Symbols</h3>
                <p className="text-muted-foreground mb-5">
                  Access hundreds of special symbols that pair perfectly with cursive and script fonts for your bio, messages, or creative projects.
                </p>
                <ul className="space-y-2 mb-5">
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Categorized symbol library</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>One-click copy feature</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Personal favorites collection</span>
                  </li>
                </ul>
                <Button variant="ghost" size="sm" className="gap-1" onClick={() => document.getElementById('symbol-library')?.scrollIntoView({ behavior: 'smooth' })}>
                  <span>Explore symbols</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Feature 4 */}
          <Card className="bg-background/70 backdrop-blur-sm transform hover:translate-y-[-5px] transition-all duration-300 overflow-hidden border-primary/10 shadow-lg">
            <CardContent className="p-0">
              <div className="h-1 bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              <div className="p-6">
                <div className="h-14 w-14 rounded-2xl bg-primary/10 flex items-center justify-center mb-5">
                  <svg className="h-7 w-7 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Cross-Platform Compatible</h3>
                <p className="text-muted-foreground mb-5">
                  All generated fonts and symbols work across most platforms, including Instagram, 
                  Twitter, Facebook, Discord, TikTok, WhatsApp and more.
                </p>
                <ul className="space-y-2 mb-5">
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Universal Unicode support</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Works on all modern devices</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>No special apps required</span>
                  </li>
                </ul>
                <Button variant="ghost" size="sm" className="gap-1" onClick={() => document.getElementById('what-is')?.scrollIntoView({ behavior: 'smooth' })}>
                  <span>Learn more</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Feature 5 */}
          <Card className="bg-background/70 backdrop-blur-sm transform hover:translate-y-[-5px] transition-all duration-300 overflow-hidden border-primary/10 shadow-lg">
            <CardContent className="p-0">
              <div className="h-1 bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              <div className="p-6">
                <div className="h-14 w-14 rounded-2xl bg-primary/10 flex items-center justify-center mb-5">
                  <svg className="h-7 w-7 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Beautiful Design</h3>
                <p className="text-muted-foreground mb-5">
                  User-friendly interface that's easy on the eyes and intuitive to use. 
                  The clean design helps you focus on creating the perfect text style.
                </p>
                <ul className="space-y-2 mb-5">
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Modern, responsive interface</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Smooth animations & transitions</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Intuitive visual organization</span>
                  </li>
                </ul>
                <Button variant="ghost" size="sm" className="gap-1" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
                  <span>Back to top</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Feature 6 */}
          <Card className="bg-background/70 backdrop-blur-sm transform hover:translate-y-[-5px] transition-all duration-300 overflow-hidden border-primary/10 shadow-lg">
            <CardContent className="p-0">
              <div className="h-1 bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              <div className="p-6">
                <div className="h-14 w-14 rounded-2xl bg-primary/10 flex items-center justify-center mb-5">
                  <svg className="h-7 w-7 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Lightning Fast</h3>
                <p className="text-muted-foreground mb-5">
                  No waiting, no loading. Get instant text transformations and access 
                  to all features without delay or frustrating ads.
                </p>
                <ul className="space-y-2 mb-5">
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Real-time text transformation</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>No ads or popups</span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-2 text-primary">✓</div>
                    <span>Optimized performance</span>
                  </li>
                </ul>
                <Button variant="ghost" size="sm" className="gap-1" onClick={() => document.getElementById('font-generator')?.scrollIntoView({ behavior: 'smooth' })}>
                  <span>Experience it</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="mt-16 flex justify-center">
          <div className="p-8 max-w-4xl w-full rounded-2xl bg-gradient-to-br from-purple-600/10 to-indigo-600/10 backdrop-blur-sm border border-white/10 text-center">
            <h3 className="text-2xl md:text-3xl font-bold mb-6 gradient-text">Ready to Transform Your Text?</h3>
            <p className="mb-8 text-xl opacity-90">
              Join thousands of users creating eye-catching content with our <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">font generator</a>!
              Explore our <a href="/elegant-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">premium collection</a>.
            </p>
            <Button className="h-12 px-8 text-base fancy-button bg-gradient-to-r from-purple-600 to-indigo-600 border-none" size="lg" onClick={() => document.getElementById('font-generator')?.scrollIntoView({ behavior: 'smooth' })}>
              Start Generating Now
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection