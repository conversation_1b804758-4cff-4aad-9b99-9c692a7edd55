import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, ArrowR<PERSON>, Co<PERSON>, Check, Eye } from "lucide-react"
import { fontStyles } from "@/lib/font-data"
import { trackTextCopy, trackFontGeneration } from "@/lib/analytics"

interface HeroSectionProps {
  onPreview?: (text: string, styleName: string) => void;
}

const HeroSection = ({ onPreview }: HeroSectionProps) => {
  const [text, setText] = useState("Transform your text")
  const [transformedText, setTransformedText] = useState("")
  const [copied, setCopied] = useState(false)
  const [currentFontIndex, setCurrentFontIndex] = useState(0)
  
  // Get cursive and tattoo fonts, prioritizing cursive
  const cursiveFonts = fontStyles.filter(font => font.category === 'cursive')
  const tattooFonts = fontStyles.filter(font => font.category === 'tattoo')
  
  // Combine with cursive fonts first (priority), then tattoo fonts
  const prioritizedFonts = [...cursiveFonts, ...tattooFonts]

  // Auto cycling through different font styles
  useEffect(() => {
    if (!text || prioritizedFonts.length === 0) return

    // Transform text initially
    setTransformedText(prioritizedFonts[currentFontIndex].transform(text))
    
    // Track font generation in hero section
    trackFontGeneration('hero_auto_cycle');
    
    // Set up interval for cycling through transformations
    const interval = setInterval(() => {
      setCurrentFontIndex(prev => (prev + 1) % prioritizedFonts.length)
    }, 2000)
    
    return () => clearInterval(interval)
  }, [text])
  
  // Update transformed text when the font index changes
  useEffect(() => {
    if (!text || prioritizedFonts.length === 0) return
    setTransformedText(prioritizedFonts[currentFontIndex].transform(text))
  }, [currentFontIndex])

  const handleCopy = () => {
    navigator.clipboard.writeText(transformedText)
    setCopied(true)
    
    // Track copy event from hero section
    if (prioritizedFonts.length > 0) {
      trackTextCopy('hero_' + prioritizedFonts[currentFontIndex].name);
    }
    
    setTimeout(() => {
      setCopied(false)
    }, 2000)
  }

  const handlePreview = () => {
    if (onPreview && prioritizedFonts.length > 0) {
      onPreview(transformedText, prioritizedFonts[currentFontIndex].name);
      // Track preview event from hero section
      trackFontGeneration('hero_preview_' + prioritizedFonts[currentFontIndex].name);
    }
  }

  return (
    <section className="relative py-24 md:py-32 lg:py-40 overflow-hidden" id="home">
      {/* Dynamic Background Elements */}
      <div className="animated-background">
        {Array.from({ length: 12 }).map((_, i) => (
          <span key={i}></span>
        ))}
      </div>
      
      {/* Glowing circles */}
      <div className="absolute top-1/4 left-10 w-64 h-64 bg-purple-600/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-10 w-80 h-80 bg-indigo-600/20 rounded-full blur-3xl"></div>
      
      <div className="container px-4 md:px-6 relative z-10">
        <div className="flex flex-col items-center text-center max-w-5xl mx-auto">
          <div className="inline-flex items-center justify-center px-4 py-1.5 mb-6 text-sm rounded-full bg-primary/20 text-primary border border-primary/30 animate-pulse-glow">
            <Sparkles className="h-3.5 w-3.5 mr-1.5" />
            <span>Transform ordinary text into extraordinary styles</span>
          </div>
          
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight">
            <span className="gradient-text">Best Cursive Font Generator Online</span>
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mb-10">
            Generate beautiful cursive fonts, elegant scripts and sophisticated handwriting styles for your social media bio, posts, and messages.
            <br className="hidden md:inline" />
            Our professional font generator offers the finest typography collection online.
          </p>
          
          <div className="w-full max-w-2xl mb-10">
            <div className="flex flex-col sm:flex-row gap-3">
              <Input
                className="h-14 text-lg bg-background/70 backdrop-blur-sm"
                placeholder="Enter your text to create beautiful cursive fonts with our cursive font generator..."
                value={text}
                onChange={(e) => setText(e.target.value)}
              />
            </div>
            
            {transformedText && (
              <div className="mt-6 p-6 bg-background/50 backdrop-blur-sm border-2 border-primary/30 rounded-xl text-2xl animate-float">
                <div className="flex justify-between items-center">
                  <div className="font-medium overflow-hidden overflow-ellipsis">
                    {transformedText}
                  </div>
                  <div className="flex gap-2 ml-4 flex-shrink-0">
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="border-primary/50"
                      onClick={handleCopy}
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4 mr-1" />
                          <span>Copied!</span>
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-1" />
                          <span>Copy</span>
                        </>
                      )}
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="border-blue-500/30 text-blue-600 hover:bg-blue-50"
                      onClick={handlePreview}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      <span>Preview</span>
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-background/50 backdrop-blur-sm border border-primary/20 p-6 rounded-xl hover:transform hover:scale-105 transition-all duration-300">
              <div className="h-12 w-12 bg-primary/20 flex items-center justify-center rounded-lg mb-4 mx-auto">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="font-bold text-lg mb-2">30+ Font Styles</h3>
              <p className="text-muted-foreground">Transform your text into elegant scripts, bold styles, italic fonts, and many more unique typography variations.</p>
            </div>
            
            <div className="bg-background/50 backdrop-blur-sm border border-primary/20 p-6 rounded-xl hover:transform hover:scale-105 transition-all duration-300">
              <div className="h-12 w-12 bg-primary/20 flex items-center justify-center rounded-lg mb-4 mx-auto">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="font-bold text-lg mb-2">Social Media Preview</h3>
              <p className="text-muted-foreground">See how your fonts will look on Instagram, Twitter and other platforms with our live preview feature.</p>
            </div>
            
            <div className="bg-background/50 backdrop-blur-sm border border-primary/20 p-6 rounded-xl hover:transform hover:scale-105 transition-all duration-300">
              <div className="h-12 w-12 bg-primary/20 flex items-center justify-center rounded-lg mb-4 mx-auto">
                <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                </svg>
              </div>
              <h3 className="font-bold text-lg mb-2">One-Click Copy</h3>
              <p className="text-muted-foreground">Instantly copy any style and paste it anywhere you want - no restrictions.</p>
            </div>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4">
            <Button className="h-12 px-6 text-base fancy-button bg-gradient-to-r from-purple-600 to-indigo-600 border-none" size="lg" onClick={() => document.getElementById('font-generator')?.scrollIntoView({ behavior: 'smooth' })}>
              Try All Styles
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button className="h-12 px-6 text-base fancy-button" variant="outline" size="lg" onClick={() => document.getElementById('symbol-library')?.scrollIntoView({ behavior: 'smooth' })}>
              Explore Symbols Library
            </Button>
          </div>

          <div className="flex flex-wrap justify-center gap-6 mt-6 text-sm">
            <a
              href="/cursive-fonts"
              className="text-muted-foreground hover:text-primary transition-colors font-medium"
            >
              Explore All Cursive Fonts →
            </a>
            <a
              href="/elegant-cursive-fonts"
              className="text-muted-foreground hover:text-primary transition-colors font-medium"
            >
              Elegant Cursive Fonts →
            </a>
            <a
              href="/modern-cursive-fonts"
              className="text-muted-foreground hover:text-primary transition-colors font-medium"
            >
              Modern Cursive Fonts →
            </a>
            <a
              href="/script-fonts"
              className="text-muted-foreground hover:text-primary transition-colors font-medium"
            >
              Script Fonts →
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection