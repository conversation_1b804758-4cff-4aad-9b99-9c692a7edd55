import { ReactNode, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { ChevronRight, Home } from "lucide-react"
import Header from "./Header"
import Footer from "./Footer"
import { trackPageView } from "@/lib/analytics"

interface PageLayoutProps {
  children: ReactNode
  title: string
  breadcrumbs?: { label: string; href?: string }[]
}

const PageLayout = ({ children, title, breadcrumbs = [] }: PageLayoutProps) => {
  const navigate = useNavigate();

  useEffect(() => {
    // Track page view when component mounts
    trackPageView(title);
  }, [title]);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Breadcrumbs */}
      <div className="container px-4 md:px-6 py-4 border-b">
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
          <button 
            onClick={() => navigate('/')}
            className="flex items-center hover:text-foreground transition-colors"
          >
            <Home className="h-4 w-4 mr-1" />
            Home
          </button>
          {breadcrumbs.map((breadcrumb, index) => (
            <div key={index} className="flex items-center">
              <ChevronRight className="h-4 w-4 mx-2" />
              {breadcrumb.href ? (
                <button 
                  onClick={() => navigate(breadcrumb.href!)}
                  className="hover:text-foreground transition-colors"
                >
                  {breadcrumb.label}
                </button>
              ) : (
                <span className="text-foreground font-medium">{breadcrumb.label}</span>
              )}
            </div>
          ))}
        </nav>
      </div>

      {/* Page Content */}
      <main className="container px-4 md:px-6 py-8 md:py-12">
        <h1 className="text-3xl md:text-4xl font-bold text-center mb-8 gradient-text">
          {title}
        </h1>
        {children}
      </main>

      <Footer />
    </div>
  )
}

export default PageLayout 