import { useEffect } from 'react'
import { GA_TRACKING_ID } from '@/lib/analytics'

declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
}

const GoogleAnalytics = () => {
  useEffect(() => {
    // Since gtag is loaded in HTML head, we just need to ensure it's configured
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA_TRACKING_ID, {
        page_path: window.location.pathname,
      })
    }
  }, [])

  return null
}

export default GoogleAnalytics 