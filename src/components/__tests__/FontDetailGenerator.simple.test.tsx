import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import FontDetailGenerator from '../FontDetailGenerator'
import { FontStyle } from '@/lib/font-data'

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock analytics
vi.mock('@/lib/analytics', () => ({
  trackTextCopy: vi.fn(),
  trackFontGeneration: vi.fn(),
}))

// Mock fonts data
const mockFonts: FontStyle[] = [
  {
    name: 'Cursive',
    category: 'cursive',
    description: 'Classic elegant cursive font',
    transform: (text: string) => `𝒞𝓊𝓇𝓈𝒾𝓋𝑒: ${text}`,
  },
  {
    name: 'Bold Cursive',
    category: 'cursive',
    description: 'Bold version of cursive font',
    transform: (text: string) => `𝓑𝓸𝓵𝓭: ${text}`,
  },
]

describe('FontDetailGenerator - Core Functionality', () => {
  const defaultProps = {
    fonts: mockFonts,
    category: 'cursive',
    title: 'Cursive Font Generator',
    description: 'Generate beautiful cursive fonts',
  }

  beforeEach(() => {
    localStorage.clear()
    vi.clearAllMocks()
  })

  it('renders the component with title and description', () => {
    render(<FontDetailGenerator {...defaultProps} />)
    
    expect(screen.getByText('Cursive Font Generator')).toBeInTheDocument()
    expect(screen.getByText('Generate beautiful cursive fonts')).toBeInTheDocument()
  })

  it('renders input field with default text', () => {
    render(<FontDetailGenerator {...defaultProps} />)
    
    const input = screen.getByLabelText('Enter text to convert')
    expect(input).toBeInTheDocument()
    expect(input).toHaveValue('Hello World')
  })

  it('displays fonts based on category', () => {
    render(<FontDetailGenerator {...defaultProps} />)
    
    expect(screen.getByText('Cursive')).toBeInTheDocument()
    expect(screen.getByText('Bold Cursive')).toBeInTheDocument()
  })

  it('updates transformed text when input changes', async () => {
    const user = userEvent.setup()
    render(<FontDetailGenerator {...defaultProps} />)
    
    const input = screen.getByLabelText('Enter text to convert')
    await user.clear(input)
    await user.type(input, 'Test')
    
    expect(screen.getByText('𝒞𝓊𝓇𝓈𝒾𝓋𝑒: Test')).toBeInTheDocument()
    expect(screen.getByText('𝓑𝓸𝓵𝓭: Test')).toBeInTheDocument()
  })

  it('shows usage tips section', () => {
    render(<FontDetailGenerator {...defaultProps} />)
    
    expect(screen.getByText('Usage Tips:')).toBeInTheDocument()
    expect(screen.getByText(/Click the "Copy" button to copy the font/)).toBeInTheDocument()
  })

  it('displays font descriptions when available', () => {
    render(<FontDetailGenerator {...defaultProps} />)
    
    expect(screen.getByText('Classic elegant cursive font')).toBeInTheDocument()
    expect(screen.getByText('Bold version of cursive font')).toBeInTheDocument()
  })

  it('filters fonts based on search term', async () => {
    const user = userEvent.setup()
    render(<FontDetailGenerator {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search font styles...')
    await user.type(searchInput, 'Bold')
    
    expect(screen.getByText('Bold Cursive')).toBeInTheDocument()
    expect(screen.queryByText('Cursive')).not.toBeInTheDocument()
  })

  it('shows no results message when search has no matches', async () => {
    const user = userEvent.setup()
    render(<FontDetailGenerator {...defaultProps} />)
    
    const searchInput = screen.getByPlaceholderText('Search font styles...')
    await user.type(searchInput, 'NonExistent')
    
    expect(screen.getByText('No font styles found containing "NonExistent"')).toBeInTheDocument()
  })

  it('clears input when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<FontDetailGenerator {...defaultProps} />)
    
    const clearButton = screen.getByText('Clear')
    await user.click(clearButton)
    
    const input = screen.getByLabelText('Enter text to convert')
    expect(input).toHaveValue('')
  })
})
