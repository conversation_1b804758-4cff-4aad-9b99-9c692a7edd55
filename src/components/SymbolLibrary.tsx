import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Search, Heart, Star, ArrowRight, CircleDot, Smile } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { trackSymbolCopy } from "@/lib/analytics"

interface SymbolCategory {
  id: string
  name: string
  icon: JSX.Element
  symbols: string[]
}

const SymbolLibrary = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const { toast } = useToast()
  const [activeCategory, setActiveCategory] = useState("common")
  const [favSymbols, setFavSymbols] = useState<string[]>([])

  const symbolCategories: SymbolCategory[] = [
    {
      id: "common",
      name: "Common",
      icon: <CircleDot className="h-4 w-4" />,
      symbols: ["★", "☆", "✓", "✗", "♥", "♦", "♣", "♠", "•", "○", "●", "◎", "◇", "◆", "□", "■", "△", "▲", "▽", "▼", "→", "←", "↑", "↓", "↔", "↕", "⇒", "⇐", "⇑", "⇓", "♪", "♫", "☼", "☀", "☁", "☂", "☮", "☯", "☭", "✈", "♛", "♕", "☎", "☏", "✉", "☑", "☒"]
    },
    {
      id: "hearts",
      name: "Hearts",
      icon: <Heart className="h-4 w-4" />,
      symbols: ["❤", "❤️", "💙", "💚", "💛", "💜", "🖤", "💗", "💓", "💔", "💕", "💖", "💝", "💞", "💟", "❣", "♥", "💘", "💌", "❥", "❦", "❧", "ღ", "💑"]
    },
    {
      id: "stars",
      name: "Stars",
      icon: <Star className="h-4 w-4" />,
      symbols: ["★", "☆", "✩", "✪", "✫", "✬", "✭", "✮", "✯", "✰", "⁂", "⋆", "✢", "✣", "✤", "✥", "✦", "✧", "✨", "⍟", "⊛", "✡", "❂", "✹", "✺", "✻", "✼", "❄", "❅", "❆", "❇", "❈"]
    },
    {
      id: "arrows",
      name: "Arrows",
      icon: <ArrowRight className="h-4 w-4" />,
      symbols: ["→", "←", "↑", "↓", "↕", "↔", "↗", "↘", "↙", "↖", "↪", "↩", "⤴", "⤵", "⇒", "⇐", "⇑", "⇓", "⇔", "⇗", "⇘", "⇙", "⇖", "⟹", "⟸", "⟺", "⟼", "⤍", "⤏", "⥂", "⥃", "⥄", "⟰", "⟱", "⇜", "⇝"]
    },
    {
      id: "math",
      name: "Math",
      icon: <CircleDot className="h-4 w-4" />,
      symbols: ["π", "ℝ", "ℚ", "ℤ", "ℕ", "∞", "≠", "≈", "≡", "≤", "≥", "≪", "≫", "÷", "±", "∓", "×", "⊕", "⊗", "∂", "∫", "∑", "∏", "√", "∛", "∜", "∅", "∪", "∩", "⊂", "⊃", "∈", "∉", "∀", "∃", "∄", "∴", "∵", "∇", "⊥", "⊢", "∝", "∧", "∨"]
    },
    {
      id: "emoticons",
      name: "Emoticons",
      icon: <Smile className="h-4 w-4" />,
      symbols: ["(^‿^)", "¯\\_(ツ)_/¯", "(◕‿◕)", "ʕ•ᴥ•ʔ", "(✿◠‿◠)", "(◡‿◡✿)", "(ᵔᴥᵔ)", "ʕ·ᴥ·ʔ", "ಠ_ಠ", "(╯°□°)╯︵ ┻━┻", "┬─┬ノ( º _ ºノ)", "(⌐■_■)", "ヽ(´▽`)/", "(づ￣ ³￣)づ", "♪♪ ヽ(ˇ∀ˇ )ゞ", "(ง'̀-'́)ง", "(◕‿◕✿)", "(⊙﹏⊙)", "（・∀・）", "(｡♥‿♥｡)"]
    }
  ]

  const handleSymbolClick = (symbol: string) => {
    navigator.clipboard.writeText(symbol)
    
    // Track symbol copy event
    trackSymbolCopy(symbol);
    
    toast({
      title: "Symbol copied",
      description: `Symbol "${symbol}" has been copied to your clipboard.`,
    })
  }

  const handleFavoriteToggle = (symbol: string) => {
    if (favSymbols.includes(symbol)) {
      setFavSymbols(favSymbols.filter(s => s !== symbol))
    } else {
      setFavSymbols([...favSymbols, symbol])
    }
  }

  const allSymbols = symbolCategories.flatMap(cat => cat.symbols)
  
  const filteredSymbols = searchTerm 
    ? allSymbols.filter(symbol => symbol.includes(searchTerm))
    : symbolCategories.find(cat => cat.id === activeCategory)?.symbols || []

  const isSearchActive = searchTerm.length > 0

  return (
    <section id="symbol-library" className="py-12 md:py-20 bg-secondary/30">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-center mb-4 gradient-text">Symbol Collection</h2>
          <p className="text-xl opacity-90 max-w-3xl mx-auto">
            Hundreds of cool symbols at your fingertips. Just click to copy and paste anywhere.
          </p>
        </div>

        <Card className="max-w-4xl mx-auto border shadow-lg bg-card/80 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <CardTitle className="flex items-center gap-2 gradient-text">
                <Star className="h-5 w-5" />
                <span>Symbol Library</span>
              </CardTitle>
              <div className="relative w-full md:max-w-xs">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search symbols..."
                  className="w-full pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Favorites section - only show if there are favorites */}
            {favSymbols.length > 0 && !isSearchActive && (
              <div className="mb-6">
                <h3 className="text-sm font-semibold mb-2 flex items-center">
                  <Heart className="h-4 w-4 mr-1 text-red-500" />
                  Your Favorites
                </h3>
                <div className="grid grid-cols-8 sm:grid-cols-10 md:grid-cols-12 gap-2">
                  {favSymbols.map((symbol, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="h-10 w-10 p-0 flex items-center justify-center text-lg hover:bg-primary hover:text-primary-foreground symbol-item"
                      onClick={() => handleSymbolClick(symbol)}
                    >
                      {symbol}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {!isSearchActive && (
              <Tabs defaultValue="common" value={activeCategory} onValueChange={setActiveCategory}>
                <TabsList className="w-full mb-4 flex flex-wrap h-auto">
                  {symbolCategories.map((category) => (
                    <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-1">
                      {category.icon}
                      {category.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
                {symbolCategories.map((category) => (
                  <TabsContent key={category.id} value={category.id} className="mt-0">
                    <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 gap-2">
                      {category.symbols.map((symbol, index) => (
                        <div
                          key={index}
                          className="relative group"
                        >
                          <div 
                            className={`flex items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors text-lg symbol-item ${favSymbols.includes(symbol) ? 'border-primary' : ''}`}
                            onClick={() => handleSymbolClick(symbol)}
                          >
                            {symbol}
                          </div>
                          <button
                            className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity h-5 w-5 rounded-full bg-muted text-muted-foreground hover:bg-secondary hover:text-secondary-foreground flex items-center justify-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFavoriteToggle(symbol);
                            }}
                          >
                            {favSymbols.includes(symbol) ? "−" : "+"}
                          </button>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            )}

            {isSearchActive && (
              <div className="mt-4">
                <div className="text-sm mb-4 flex items-center">
                  <Search className="h-4 w-4 mr-1" />
                  Search results for "{searchTerm}":
                </div>
                {filteredSymbols.length > 0 ? (
                  <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 gap-2">
                    {filteredSymbols.map((symbol, index) => (
                      <div
                        key={index}
                        className="relative group"
                      >
                        <div
                          className={`flex items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors text-lg symbol-item ${favSymbols.includes(symbol) ? 'border-primary' : ''}`}
                          onClick={() => handleSymbolClick(symbol)}
                        >
                          {symbol}
                        </div>
                        <button
                          className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity h-5 w-5 rounded-full bg-muted text-muted-foreground hover:bg-secondary hover:text-secondary-foreground flex items-center justify-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleFavoriteToggle(symbol);
                          }}
                        >
                          {favSymbols.includes(symbol) ? "−" : "+"}
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No matching symbols found
                  </div>
                )}
              </div>
            )}
            
            <div className="mt-6 text-sm text-muted-foreground text-center">
              Click any symbol to copy it to your clipboard. <br/>
              Use the + button to save symbols to your favorites for quick access.
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}

export default SymbolLibrary