import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from "@/components/ui/card";
import { CategoryKey, FONT_CATEGORIES } from '@/config/font-categories';

interface RelatedFontsProps {
  currentCategory: CategoryKey;
  className?: string;
}

const RelatedFonts: React.FC<RelatedFontsProps> = ({ currentCategory, className = "" }) => {
  // 获取相关字体分类的推荐逻辑 - 优先推荐cursive相关字体
  const getRelatedCategories = (category: CategoryKey): CategoryKey[] => {
    const relatedMap: Record<CategoryKey, CategoryKey[]> = {
      'cursive-fonts': ['elegant-cursive-fonts', 'modern-cursive-fonts', 'vintage-cursive-fonts', 'romantic-cursive-fonts'],
      'elegant-cursive-fonts': ['cursive-fonts', 'modern-cursive-fonts', 'script-fonts', 'calligraphy-fonts'],
      'modern-cursive-fonts': ['cursive-fonts', 'elegant-cursive-fonts', 'script-fonts', 'handwriting-fonts'],
      'vintage-cursive-fonts': ['cursive-fonts', 'elegant-cursive-fonts', 'script-fonts', 'calligraphy-fonts'],
      'romantic-cursive-fonts': ['cursive-fonts', 'elegant-cursive-fonts', 'script-fonts', 'handwriting-fonts'],
      'script-fonts': ['cursive-fonts', 'elegant-cursive-fonts', 'handwriting-fonts', 'calligraphy-fonts'],
      'elegant-script-fonts': ['cursive-fonts', 'elegant-cursive-fonts', 'script-fonts', 'calligraphy-fonts'],
      'handwriting-fonts': ['cursive-fonts', 'script-fonts', 'elegant-cursive-fonts', 'modern-cursive-fonts'],
      'calligraphy-fonts': ['cursive-fonts', 'elegant-cursive-fonts', 'script-fonts', 'handwriting-fonts'],
      'tattoo-fonts': ['gothic-fonts', 'old-english-fonts', 'tattoo-script-fonts', 'cursive-fonts'],
      'gothic-fonts': ['tattoo-fonts', 'old-english-fonts', 'tattoo-script-fonts', 'cursive-fonts'],
      'old-english-fonts': ['gothic-fonts', 'tattoo-fonts', 'tattoo-script-fonts', 'cursive-fonts'],
      'tattoo-script-fonts': ['tattoo-fonts', 'gothic-fonts', 'old-english-fonts', 'cursive-fonts'],
      'bold-fonts': ['cursive-fonts', 'tattoo-fonts', 'gothic-fonts', 'vaporwave-fonts'],
      'italic-fonts': ['cursive-fonts', 'handwriting-fonts', 'elegant-cursive-fonts', 'script-fonts'],
      'small-caps-fonts': ['cursive-fonts', 'italic-fonts', 'bold-fonts', 'old-english-fonts'],
      'vaporwave-fonts': ['cursive-fonts', 'bold-fonts', 'italic-fonts', 'tattoo-fonts']
    };

    return relatedMap[category] || [];
  };

  const relatedCategories = getRelatedCategories(currentCategory).slice(0, 4);

  if (relatedCategories.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <h2 className="text-2xl font-bold text-center">Related Font Generators</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {relatedCategories.map((categoryKey) => {
          const config = FONT_CATEGORIES[categoryKey];
          return (
            <Card key={categoryKey} className="hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-6">
                <Link 
                  to={`/${categoryKey}`}
                  className="block group"
                >
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {config.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {config.description}
                  </p>
                  <div className="mt-3 text-primary text-sm font-medium group-hover:underline">
                    Try this generator →
                  </div>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default RelatedFonts;
