import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON>rk<PERSON>, <PERSON><PERSON>, Check, Instagram, Twitter, MessageCircle, Facebook } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { useIsMobile } from "@/hooks/use-mobile"

interface PreviewSectionProps {
  initialText?: string;
  initialStyle?: string;
}

const PreviewSection = ({ 
  initialText = "Your stylish text here", 
  initialStyle = "circle" 
}: PreviewSectionProps) => {
  const [text, setText] = useState<string>(initialText)
  const [previewType, setPreviewType] = useState<string>("instagram")
  const [style, setStyle] = useState<string>(initialStyle)
  const [styledText, setStyledText] = useState<string>("")
  const [copied, setCopied] = useState<boolean>(false)
  const { toast } = useToast()
  const isMobile = useIsMobile()
  
  // Update text when initialText prop changes
  useEffect(() => {
    setText(initialText)
  }, [initialText])
  
  // Update style when initialStyle prop changes
  useEffect(() => {
    setStyle(initialStyle)
  }, [initialStyle])
  
  // Apply the selected font style
  const applyStyle = (text: string, style: string): string => {
    if (!text) return ""

    const transformations: Record<string, (t: string) => string> = {
      bold: (t) => t.split("").map(c => {
        const boldChar = {
          'a': '𝗮', 'b': '𝗯', 'c': '𝗰', 'd': '𝗱', 'e': '𝗲', 'f': '𝗳', 'g': '𝗴', 'h': '𝗵', 'i': '𝗶', 'j': '𝗷',
          'k': '𝗸', 'l': '𝗹', 'm': '𝗺', 'n': '𝗻', 'o': '𝗼', 'p': '𝗽', 'q': '𝗾', 'r': '𝗿', 's': '𝘀', 't': '𝘁',
          'u': '𝘂', 'v': '𝘃', 'w': '𝘄', 'x': '𝘅', 'y': '𝘆', 'z': '𝘇',
          'A': '𝗔', 'B': '𝗕', 'C': '𝗖', 'D': '𝗗', 'E': '𝗘', 'F': '𝗙', 'G': '𝗚', 'H': '𝗛', 'I': '𝗜', 'J': '𝗝',
          'K': '𝗞', 'L': '𝗟', 'M': '𝗠', 'N': '𝗡', 'O': '𝗢', 'P': '𝗣', 'Q': '𝗤', 'R': '𝗥', 'S': '𝗦', 'T': '𝗧',
          'U': '𝗨', 'V': '𝗩', 'W': '𝗪', 'X': '𝗫', 'Y': '𝗬', 'Z': '𝗭',
          '0': '𝟬', '1': '𝟭', '2': '𝟮', '3': '𝟯', '4': '𝟰', '5': '𝟱', '6': '𝟲', '7': '𝟳', '8': '𝟴', '9': '𝟵'
        }[c] || c
        return boldChar
      }).join(""),
      
      italic: (t) => t.split("").map(c => {
        const italicChar = {
          'a': '𝘢', 'b': '𝘣', 'c': '𝘤', 'd': '𝘥', 'e': '𝘦', 'f': '𝘧', 'g': '𝘨', 'h': '𝘩', 'i': '𝘪', 'j': '𝘫',
          'k': '𝘬', 'l': '𝘭', 'm': '𝘮', 'n': '𝘯', 'o': '𝘰', 'p': '𝘱', 'q': '𝘲', 'r': '𝘳', 's': '𝘴', 't': '𝘵',
          'u': '𝘶', 'v': '𝘷', 'w': '𝘸', 'x': '𝘹', 'y': '𝘺', 'z': '𝘻',
          'A': '𝘈', 'B': '𝘉', 'C': '𝘊', 'D': '𝘋', 'E': '𝘌', 'F': '𝘍', 'G': '𝘎', 'H': '𝘏', 'I': '𝘐', 'J': '𝘑',
          'K': '𝘒', 'L': '𝘓', 'M': '𝘔', 'N': '𝘕', 'O': '𝘖', 'P': '𝘗', 'Q': '𝘘', 'R': '𝘙', 'S': '𝘚', 'T': '𝘛',
          'U': '𝘜', 'V': '𝘝', 'W': '𝘞', 'X': '𝘟', 'Y': '𝘠', 'Z': '𝘡'
        }[c] || c
        return italicChar
      }).join(""),
      
      circle: (t) => t.split("").map(c => {
        const circleChar = {
          'a': 'ⓐ', 'b': 'ⓑ', 'c': 'ⓒ', 'd': 'ⓓ', 'e': 'ⓔ', 'f': 'ⓕ', 'g': 'ⓖ', 'h': 'ⓗ', 'i': 'ⓘ', 'j': 'ⓙ',
          'k': 'ⓚ', 'l': 'ⓛ', 'm': 'ⓜ', 'n': 'ⓝ', 'o': 'ⓞ', 'p': 'ⓟ', 'q': 'ⓠ', 'r': 'ⓡ', 's': 'ⓢ', 't': 'ⓣ',
          'u': 'ⓤ', 'v': 'ⓥ', 'w': 'ⓦ', 'x': 'ⓧ', 'y': 'ⓨ', 'z': 'ⓩ',
          'A': 'Ⓐ', 'B': 'Ⓑ', 'C': 'Ⓒ', 'D': 'Ⓓ', 'E': 'Ⓔ', 'F': 'Ⓕ', 'G': 'Ⓖ', 'H': 'Ⓗ', 'I': 'Ⓘ', 'J': 'Ⓙ',
          'K': 'Ⓚ', 'L': 'Ⓛ', 'M': 'Ⓜ', 'N': 'Ⓝ', 'O': 'Ⓞ', 'P': 'Ⓟ', 'Q': 'Ⓠ', 'R': 'Ⓡ', 'S': 'Ⓢ', 'T': 'Ⓣ',
          'U': 'Ⓤ', 'V': 'Ⓥ', 'W': 'Ⓦ', 'X': 'Ⓧ', 'Y': 'Ⓨ', 'Z': 'Ⓩ',
          '0': '⓪', '1': '①', '2': '②', '3': '③', '4': '④', '5': '⑤', '6': '⑥', '7': '⑦', '8': '⑧', '9': '⑨'
        }[c] || c
        return circleChar
      }).join(""),
      
      script: (t) => t.split("").map(c => {
        const scriptChar = {
          'A': '𝒜', 'B': 'ℬ', 'C': '𝒞', 'D': '𝒟', 'E': 'ℰ', 'F': 'ℱ', 'G': '𝒢', 'H': 'ℋ', 'I': 'ℐ', 'J': '𝒥',
          'K': '𝒦', 'L': 'ℒ', 'M': 'ℳ', 'N': '𝒩', 'O': '𝒪', 'P': '𝒫', 'Q': '𝒬', 'R': 'ℛ', 'S': '𝒮', 'T': '𝒯',
          'U': '𝒰', 'V': '𝒱', 'W': '𝒲', 'X': '𝒳', 'Y': '𝒴', 'Z': '𝒵',
          'a': '𝒶', 'b': '𝒷', 'c': '𝒸', 'd': '𝒹', 'e': 'ℯ', 'f': '𝒻', 'g': 'ℊ', 'h': '𝒽', 'i': '𝒾', 'j': '𝒿',
          'k': '𝓀', 'l': '𝓁', 'm': '𝓂', 'n': '𝓃', 'o': 'ℴ', 'p': '𝓅', 'q': '𝓆', 'r': '𝓇', 's': '𝓈', 't': '𝓉',
          'u': '𝓊', 'v': '𝓋', 'w': '𝓌', 'x': '𝓍', 'y': '𝓎', 'z': '𝓏'
        }[c] || c
        return scriptChar
      }).join(""),
      
      double: (t) => t.split("").map(c => {
        const doubleStruckChar = {
          'A': '𝔸', 'B': '𝔹', 'C': 'ℂ', 'D': '𝔻', 'E': '𝔼', 'F': '𝔽', 'G': '𝔾', 'H': 'ℍ', 'I': '𝕀', 'J': '𝕁',
          'K': '𝕂', 'L': '𝕃', 'M': '𝕄', 'N': 'ℕ', 'O': '𝕆', 'P': 'ℙ', 'Q': 'ℚ', 'R': 'ℝ', 'S': '𝕊', 'T': '𝕋',
          'U': '𝕌', 'V': '𝕍', 'W': '𝕎', 'X': '𝕏', 'Y': '𝕐', 'Z': 'ℤ',
          'a': '𝕒', 'b': '𝕓', 'c': '𝕔', 'd': '𝕕', 'e': '𝕖', 'f': '𝕗', 'g': '𝕘', 'h': '𝕙', 'i': '𝕚', 'j': '𝕛',
          'k': '𝕜', 'l': '𝕝', 'm': '𝕞', 'n': '𝕟', 'o': '𝕠', 'p': '𝕡', 'q': '𝕢', 'r': '𝕣', 's': '𝕤', 't': '𝕥',
          'u': '𝕦', 'v': '𝕧', 'w': '𝕨', 'x': '𝕩', 'y': '𝕪', 'z': '𝕫',
          '0': '𝟘', '1': '𝟙', '2': '𝟚', '3': '𝟛', '4': '𝟜', '5': '𝟝', '6': '𝟞', '7': '𝟟', '8': '𝟠', '9': '𝟡'
        }[c] || c
        return doubleStruckChar
      }).join(""),
      
      vaporwave: (t) => t.split("").map(c => {
        return c.replace(/[a-zA-Z0-9]/g, (char) => {
          const code = char.charCodeAt(0)
          if (code >= 65 && code <= 90) return String.fromCharCode(code + 0xFEE0)
          if (code >= 97 && code <= 122) return String.fromCharCode(code + 0xFEE0)
          if (code >= 48 && code <= 57) return String.fromCharCode(code + 0xFEE0)
          return char
        })
      }).join(""),
      
      small: (t) => t.split("").map(c => {
        const smallChar = {
          'a': 'ᴀ', 'b': 'ʙ', 'c': 'ᴄ', 'd': 'ᴅ', 'e': 'ᴇ', 'f': 'ꜰ', 'g': 'ɢ', 'h': 'ʜ', 'i': 'ɪ', 'j': 'ᴊ',
          'k': 'ᴋ', 'l': 'ʟ', 'm': 'ᴍ', 'n': 'ɴ', 'o': 'ᴏ', 'p': 'ᴘ', 'q': 'ǫ', 'r': 'ʀ', 's': 's', 't': 'ᴛ',
          'u': 'ᴜ', 'v': 'ᴠ', 'w': 'ᴡ', 'x': 'x', 'y': 'ʏ', 'z': 'ᴢ'
        }[c.toLowerCase()] || c
        return smallChar
      }).join(""),
      
      medieval: (t) => t.split("").map(c => {
        const frakturChar = {
          'A': '𝔄', 'B': '𝔅', 'C': 'ℭ', 'D': '𝔇', 'E': '𝔈', 'F': '𝔉', 'G': '𝔊', 'H': 'ℌ', 'I': 'ℑ', 'J': '𝔍',
          'K': '𝔎', 'L': '𝔏', 'M': '𝔐', 'N': '𝔑', 'O': '𝔒', 'P': '𝔓', 'Q': '𝔔', 'R': 'ℜ', 'S': '𝔖', 'T': '𝔗',
          'U': '𝔘', 'V': '𝔙', 'W': '𝔚', 'X': '𝔛', 'Y': '𝔜', 'Z': 'ℨ',
          'a': '𝔞', 'b': '𝔟', 'c': '𝔠', 'd': '𝔡', 'e': '𝔢', 'f': '𝔣', 'g': '𝔤', 'h': '𝔥', 'i': '𝔦', 'j': '𝔧',
          'k': '𝔨', 'l': '𝔩', 'm': '𝔪', 'n': '𝔫', 'o': '𝔬', 'p': '𝔭', 'q': '𝔮', 'r': '𝔯', 's': '𝔰', 't': '𝔱',
          'u': '𝔲', 'v': '𝔳', 'w': '𝔴', 'x': '𝔵', 'y': '𝔶', 'z': '𝔷'
        }[c] || c
        return frakturChar
      }).join("")
    }

    return transformations[style] ? transformations[style](text) : text
  }

  // Update styled text when text or style changes
  useEffect(() => {
    setStyledText(applyStyle(text, style))
  }, [text, style])

  const handleCopy = () => {
    navigator.clipboard.writeText(styledText)
    setCopied(true)
    toast({
      title: "Copied to clipboard",
      description: "Your styled text is ready to paste anywhere!",
    })
    setTimeout(() => setCopied(false), 2000)
  }

  // Instagram style preview with real-time styling
  const InstagramPreview = ({ text }: { text: string }) => {
    return (
      <div className="social-preview-container max-w-md mx-auto transform transition-all duration-300 hover:shadow-2xl">
        <div className="flex items-center p-3 border-b">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full"></div>
          <div className="ml-3 font-semibold">cursivefontgen</div>
          <div className="ml-auto">•••</div>
        </div>
        <div className="aspect-square bg-gradient-to-tr from-indigo-100 to-purple-100 flex items-center justify-center p-8">
          <div className="text-center break-words text-xl font-medium animate-pulse-slow">
            {styledText}
          </div>
        </div>
        <div className="p-3">
          <div className="flex mb-2">
            <div className="mr-4">♥</div>
            <div className="mr-4">💬</div>
            <div>🔗</div>
            <div className="ml-auto">🏁</div>
          </div>
          <div className="font-semibold text-sm mb-1">268 likes</div>
          <div className="text-sm">
            <span className="font-semibold">cursivefontgen</span> Created with Cursive Font Generator
          </div>
          <div className="text-xs text-gray-500 mt-1">View all 42 comments</div>
        </div>
      </div>
    )
  }

  // Twitter/X style preview with real-time styling
  const TwitterPreview = ({ text }: { text: string }) => {
    return (
      <div className="social-preview-container max-w-md mx-auto transform transition-all duration-300 hover:shadow-2xl">
        <div className="flex p-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mr-3"></div>
          <div>
            <div className="flex items-center">
              <div className="font-bold">Cursive Font Gen</div>
              <div className="text-gray-500 ml-2 text-sm">@cursivefontgen</div>
              <div className="text-gray-500 text-sm ml-2">· 2h</div>
            </div>
            <div className="mt-1 text-lg">
              {styledText}
            </div>
            <div className="mt-3 flex text-gray-500 text-sm justify-between">
              <div className="flex items-center">
                <MessageCircle className="h-3.5 w-3.5 mr-1" />
                <span>24</span>
              </div>
              <div>🔄 58</div>
              <div>♥ 293</div>
              <div>📊 12.4K</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Facebook style preview
  const FacebookPreview = ({ text }: { text: string }) => {
    return (
      <div className="social-preview-container max-w-md mx-auto transform transition-all duration-300 hover:shadow-2xl">
        <div className="p-3 border-b">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full"></div>
            <div className="ml-2">
              <div className="font-semibold">Cursive Font Generator</div>
              <div className="text-xs text-gray-500">Just now · 🌎</div>
            </div>
            <div className="ml-auto">•••</div>
          </div>
        </div>
        <div className="p-4">
          <div className="text-lg mb-4">
            {styledText}
          </div>
          <div className="bg-gradient-to-tr from-indigo-100 to-blue-100 h-40 flex items-center justify-center rounded">
            <div className="text-2xl font-bold gradient-text">{styledText}</div>
          </div>
        </div>
        <div className="border-t p-2">
          <div className="flex justify-between text-sm text-gray-500">
            <div>❤️ 148</div>
            <div>34 Comments · 12 Shares</div>
          </div>
          <div className="flex justify-between mt-2 pt-2 border-t">
            <div className="flex-1 text-center py-1">👍 Like</div>
            <div className="flex-1 text-center py-1">💬 Comment</div>
            <div className="flex-1 text-center py-1">↗️ Share</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <section id="preview-section" className="py-12 md:py-24 bg-gradient-to-b from-background to-secondary/30 relative overflow-hidden">
      {/* Floating background elements */}
      <div className="absolute -top-20 -right-20 w-64 h-64 bg-purple-600/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"></div>
      
      <div className="container px-4 md:px-6 relative z-10">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-4 gradient-text">
            Live Social Media Preview
          </h2>
          <p className="text-xl opacity-90 max-w-3xl mx-auto">
            See exactly how your styled text will appear on your favorite platforms before posting.
            What you see is what you get!
          </p>
        </div>

        <Card className="max-w-5xl mx-auto border shadow-xl bg-card/80 backdrop-blur-sm transform transition hover:translate-y-[-5px]">
          <CardHeader className="border-b bg-muted/30">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <span className="gradient-text">Real-time Preview</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-8">
              <div className={`grid ${isMobile ? 'grid-cols-1 gap-8' : 'md:grid-cols-2 gap-6'}`}>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium block mb-1">Your Text</label>
                    <textarea 
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      placeholder="Enter your text here"
                      className="w-full min-h-[100px] p-4 border rounded-md focus:ring-2 focus:ring-primary/50 transition-all"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium block mb-1">Platform</label>
                      <Select value={previewType} onValueChange={setPreviewType}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select platform" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="instagram">
                            <div className="flex items-center gap-2">
                              <Instagram className="h-4 w-4" />
                              <span>Instagram</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="twitter">
                            <div className="flex items-center gap-2">
                              <Twitter className="h-4 w-4" />
                              <span>Twitter</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="facebook">
                            <div className="flex items-center gap-2">
                              <Facebook className="h-4 w-4" />
                              <span>Facebook</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium block mb-1">Font Style</label>
                      <Select value={style} onValueChange={setStyle}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select style" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bold">Bold</SelectItem>
                          <SelectItem value="italic">Italic</SelectItem>
                          <SelectItem value="circle">Circle</SelectItem>
                          <SelectItem value="script">Script</SelectItem>
                          <SelectItem value="double">Double Struck</SelectItem>
                          <SelectItem value="vaporwave">Vaporwave</SelectItem>
                          <SelectItem value="small">Small Caps</SelectItem>
                          <SelectItem value="medieval">Medieval</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="mt-2 flex flex-col gap-3">
                    <div className="p-4 border rounded-md bg-muted/30">
                      <div className="text-sm font-medium mb-1">Generated Style:</div>
                      <div className="text-lg break-words p-2">{styledText}</div>
                    </div>
                    
                    <Button 
                      onClick={handleCopy} 
                      className="ml-auto gap-2 fancy-button bg-gradient-to-r from-purple-600 to-indigo-600 border-none"
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      {copied ? "Copied!" : "Copy Text"}
                    </Button>
                  </div>
                </div>
                
                <div className="preview-section relative">
                  <div className="absolute inset-0 bg-gradient-to-tr from-indigo-500/5 to-purple-500/5 rounded-xl"></div>
                  <div className="text-sm font-medium mb-4">Preview:</div>
                  <div className="border rounded-xl shadow-lg p-6 bg-background/80 backdrop-blur-sm relative hover:shadow-2xl transition-all duration-500 h-full flex items-center justify-center">
                    <div className="w-full max-w-sm">
                      {previewType === "instagram" && <InstagramPreview text={text} />}
                      {previewType === "twitter" && <TwitterPreview text={text} />}
                      {previewType === "facebook" && <FacebookPreview text={text} />}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-lg font-medium mb-2 text-center">Popular Social Media Platforms</h3>
                <div className="flex flex-wrap justify-center gap-4 mt-3">
                  {["Instagram", "Twitter", "Facebook", "TikTok", "LinkedIn", "Pinterest", "Discord", "Snapchat"].map(platform => (
                    <div 
                      key={platform}
                      className="px-3 py-1.5 bg-background border rounded-full text-sm hover:bg-primary hover:text-white transition-colors cursor-pointer"
                      onClick={() => {
                        const lowerPlatform = platform.toLowerCase();
                        if (["instagram", "twitter", "facebook"].includes(lowerPlatform)) {
                          setPreviewType(lowerPlatform);
                        } else {
                          toast({ 
                            title: "Coming Soon", 
                            description: `${platform} preview will be available soon!` 
                          });
                        }
                      }}
                    >
                      {platform}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="mt-8 text-center">
          <p className="text-muted-foreground">Our font styles work universally across all major platforms.</p>
          <p className="text-sm mt-2">Get the perfect look before you post!</p>
        </div>
      </div>
    </section>
  )
}

export default PreviewSection