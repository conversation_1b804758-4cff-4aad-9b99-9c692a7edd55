import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { CategoryKey, FONT_CATEGORIES } from '@/config/font-categories';

interface BreadcrumbProps {
  categoryKey: CategoryKey;
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ categoryKey, className = "" }) => {
  const config = FONT_CATEGORIES[categoryKey];
  
  // 根据分类确定父级分类
  const getParentCategory = (category: CategoryKey): { name: string; path?: string } | null => {
    const categoryType = config.category;
    
    switch (categoryType) {
      case 'cursive':
        return { name: 'Cursive Fonts', path: '/cursive-fonts' };
      case 'tattoo':
        return { name: 'Tattoo Fonts', path: '/tattoo-fonts' };
      case 'bold':
      case 'italic':
      case 'small-caps':
      case 'vaporwave':
        return { name: 'Font Styles' };
      default:
        return null;
    }
  };

  const parentCategory = getParentCategory(categoryKey);
  
  return (
    <nav className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`} aria-label="Breadcrumb">
      <Link 
        to="/" 
        className="flex items-center hover:text-primary transition-colors"
        aria-label="Home"
      >
        <Home className="h-4 w-4" />
        <span className="ml-1">Home</span>
      </Link>
      
      <ChevronRight className="h-4 w-4 text-gray-400" />
      
      {parentCategory && parentCategory.path && parentCategory.path !== `/${categoryKey}` && (
        <>
          <Link 
            to={parentCategory.path}
            className="hover:text-primary transition-colors"
          >
            {parentCategory.name}
          </Link>
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </>
      )}
      
      {parentCategory && !parentCategory.path && (
        <>
          <span className="text-gray-500">{parentCategory.name}</span>
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </>
      )}
      
      <span className="text-gray-900 font-medium" aria-current="page">
        {config.title.replace('Free ', '').replace(' Generator', '')}
      </span>
    </nav>
  );
};

export default Breadcrumb;
