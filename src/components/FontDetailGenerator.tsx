import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Copy, Check, Search, Wand, RefreshCcw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { FontStyle } from "@/lib/font-data";
import { trackTextCopy, trackFontGeneration } from "@/lib/analytics";

interface FontDetailGeneratorProps {
  fonts: FontStyle[];
  category: string;
  title?: string;
  description?: string;
  onPreview?: (text: string, styleName: string) => void;
}

const FontDetailGenerator = ({ 
  fonts, 
  category, 
  title = "Font Generator",
  description = "Enter text and choose your favorite font style",
  onPreview 
}: FontDetailGeneratorProps) => {
  const [inputText, setInputText] = useState<string>("Hello World");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const { toast } = useToast();
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});

  // 使用分类特定的localStorage key
  const storageKey = `fontGenerator_${category}`;

  useEffect(() => {
    const savedText = localStorage.getItem(storageKey);
    if (savedText) {
      setInputText(savedText);
    }
  }, [storageKey]);

  useEffect(() => {
    localStorage.setItem(storageKey, inputText);
    // Track font generation when text changes
    if (inputText.trim()) {
      trackFontGeneration(`${category}_text_input`);
    }
  }, [inputText, storageKey, category]);

  const handleCopy = async (text: string, fontName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [fontName]: true }));

      // Track copy event
      trackTextCopy(`${category}_${fontName}`);

      // Call onPreview callback if provided
      if (onPreview) {
        onPreview(text, fontName);
      }

      toast({
        title: "Copied to clipboard!",
        description: `"${fontName}" style is ready to paste.`,
      });

      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [fontName]: false }));
      }, 2000);
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Please manually select and copy the text",
        variant: "destructive",
      });
    }
  };

  const handlePreview = (text: string, fontName: string) => {
    if (onPreview) {
      onPreview(text, fontName);
    }
    // Track preview event
    trackFontGeneration(`${category}_${fontName}_preview`);
  };

  // 过滤字体
  const filteredFonts = fonts.filter(font => 
    font.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <section className="py-12 md:py-16">
      <div className="container px-4 md:px-6">
        <Card className="max-w-4xl mx-auto border shadow-lg bg-card/80 backdrop-blur-sm">
          <CardHeader className="border-b bg-muted/30">
            <CardTitle className="flex items-center gap-2">
              <Wand className="h-5 w-5 text-primary" />
              <span className="gradient-text">{title}</span>
            </CardTitle>
            {description && (
              <p className="text-muted-foreground mt-2">{description}</p>
            )}
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              {/* 输入区域 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="text-input" className="text-sm font-medium">
                    Enter your text
                  </label>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 text-xs" 
                    onClick={() => setInputText("")}
                  >
                    <RefreshCcw className="h-3 w-3 mr-1" />
                    Clear
                  </Button>
                </div>
                <Input
                  id="text-input"
                  placeholder="Type your text here..."
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="w-full p-4 text-base border-2 border-primary/20 focus:border-primary/50 focus:ring-2 focus:ring-primary/20"
                  aria-label="Enter text to convert"
                />
              </div>

              {/* 搜索区域 */}
              <div className="flex justify-between items-center">
                <div className="text-lg font-semibold gradient-text">
                  Font Styles ({filteredFonts.length})
                </div>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search font styles..."
                    className="w-full pl-9 h-9"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 字体列表 */}
              <div className="space-y-4">
                {filteredFonts.length === 0 ? (
                  <div className="p-8 text-center text-muted-foreground">
                    <div className="mb-2">
                      No font styles found containing "{searchTerm}"
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setSearchTerm("")}
                    >
                      Clear Search
                    </Button>
                  </div>
                ) : (
                  <div className="divide-y border rounded-md bg-background/50 backdrop-blur-sm shadow-inner">
                    {filteredFonts.map((font) => {
                      const transformedText = font.transform(inputText);
                      const isCopied = copiedStates[font.name] || false;
                      
                      return (
                        <div 
                          key={font.name} 
                          className="flex items-center justify-between p-4 hover:bg-accent/30 transition-colors"
                        >
                          <div className="flex-1">
                            <div className="text-xs font-medium text-muted-foreground mb-1">
                              {font.name}
                            </div>
                            <div className="text-xl break-all">
                              {transformedText}
                            </div>
                            {font.description && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {font.description}
                              </div>
                            )}
                          </div>
                          <div className="flex gap-2 ml-4">
                            <Button
                              size="sm"
                              variant={isCopied ? "default" : "outline"}
                              className={`h-9 gap-1.5 w-[90px] ${
                                isCopied 
                                  ? "bg-primary text-primary-foreground" 
                                  : "border-primary/30"
                              }`}
                              onClick={() => handleCopy(transformedText, font.name)}
                              aria-label={`Copy ${font.name} style text: ${transformedText}`}
                            >
                              {isCopied ? (
                                <>
                                  <Check className="h-3.5 w-3.5" />
                                  <span>Copied</span>
                                </>
                              ) : (
                                <>
                                  <Copy className="h-3.5 w-3.5" />
                                  <span>Copy</span>
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Usage Tips */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">Usage Tips:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Click the "Copy" button to copy the font to your clipboard</li>
                  <li>• Paste and use in any text-supporting platform</li>
                  <li>• Test font display on your target platform first</li>
                  <li>• Different devices and apps may have varying Unicode support</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default FontDetailGenerator;
