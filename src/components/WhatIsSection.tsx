import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { PenTool, Instagram, Edit } from "lucide-react"

const WhatIsSection = () => {
  return (
    <section id="what-is" className="py-16 md:py-24 bg-slate-50/50">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-gray-800">
            What is a Cursive Font Generator?
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            Discover how to add a touch of elegance to your digital text with beautiful script and handwriting styles.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-10 max-w-5xl mx-auto items-start">
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-800 flex items-center">
              <PenTool className="h-6 w-6 mr-3 text-primary" />
              The Art of Digital Calligraphy
            </h3>
            
            <p className="text-gray-700 leading-relaxed">
              A <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">Cursive Font Generator</a> is a tool that transforms your standard text into a variety of elegant cursive and script styles. It doesn't install new fonts on your system. Instead, it uses a clever system called Unicode.
              Try our <a href="/elegant-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">elegant cursive fonts</a> for sophisticated designs.
            </p>
            
            <p className="text-gray-700 leading-relaxed">
              Unicode is a universal character encoding standard that includes thousands of symbols, including the stylish letters that form cursive text. Our generator maps your input to these special characters, creating text that looks handwritten. Because it's just text, you can copy and paste it anywhere!
            </p>
            
            <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h4 className="font-semibold mb-2 text-gray-700">How It Works: An Example</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-gray-50 rounded">
                  <div className="text-xs text-gray-500 mb-1">Your Text:</div>
                  <div className="text-lg font-sans">Elegance</div>
                </div>
                <div className="p-3 bg-gray-50 rounded">
                  <div className="text-xs text-gray-500 mb-1">Cursive Style:</div>
                  <div className="text-lg font-serif">𝓔𝓵𝓮𝓰𝓪𝓷𝓬𝓮</div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-6">
            <Card className="bg-white shadow-md hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Instagram className="h-5 w-5 mr-3 text-pink-500" />
                  Perfect for Social Media
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Make your profiles, bios, and posts on Instagram, TikTok, and other platforms stand out. A touch of
                  <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline ml-1">cursive fonts</a> adds personality and sophistication.
                  Explore our <a href="/modern-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">modern cursive collection</a> for trendy social media content.
                </p>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-center"><CheckIcon className="h-4 w-4 mr-2 text-green-500" /> Stylish Instagram Bios</li>
                  <li className="flex items-center"><CheckIcon className="h-4 w-4 mr-2 text-green-500" /> Eye-catching TikTok Captions</li>
                  <li className="flex items-center"><CheckIcon className="h-4 w-4 mr-2 text-green-500" /> Unique Facebook Posts</li>
                </ul>
              </CardContent>
            </Card>
            
            <Card className="bg-white shadow-md hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Edit className="h-5 w-5 mr-3 text-indigo-500" />
                  Tattoo Design Inspiration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Exploring fonts for a tattoo? Our generator is the perfect starting point. Generate ideas for names, quotes, or meaningful words in beautiful script. Find a style you love and show it to your tattoo artist as a reference.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}

// Helper component for the check icon
const CheckIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polyline points="20 6 9 17 4 12" />
  </svg>
)

export default WhatIsSection