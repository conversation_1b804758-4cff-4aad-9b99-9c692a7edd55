import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Co<PERSON>, Check, Search, Wand, Refresh<PERSON>c<PERSON>, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { fontStyles, FontStyle } from "@/lib/font-data";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { trackTextCopy, trackFontGeneration } from "@/lib/analytics";

interface FontGeneratorProps {
  onPreview?: (text: string, styleName: string) => void;
}

const FontGenerator = ({ onPreview }: FontGeneratorProps) => {
  const [inputText, setInputText] = useState<string>("Hello World");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [activeCategory, setActiveCategory] = useState<string>("cursive");
  const { toast } = useToast();
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const savedText = localStorage.getItem("fontGeneratorText");
    if (savedText) {
      setInputText(savedText);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("fontGeneratorText", inputText);
    // Track font generation when text changes
    if (inputText.trim()) {
      trackFontGeneration('text_input');
    }
  }, [inputText]);

  const handleCopy = (text: string, fontName: string) => {
    navigator.clipboard.writeText(text);
    setCopiedStates(prev => ({ ...prev, [fontName]: true }));
    
    // Track copy event
    trackTextCopy(fontName);
    
    toast({
      title: "Copied to clipboard!",
      description: `"${fontName}" style is ready to paste.`,
    });
    
    setTimeout(() => {
      setCopiedStates(prev => ({ ...prev, [fontName]: false }));
    }, 2000);
  };

  const handlePreview = (text: string, fontName: string) => {
    if (onPreview) {
      onPreview(text, fontName);
    }
    // Track preview event
    trackFontGeneration(fontName + '_preview');
  };

  const renderFontList = (category: FontStyle['category'] | 'all') => {
    let stylesToRender = fontStyles;
    if (category !== 'all') {
      stylesToRender = fontStyles.filter(style => style.category === category);
    }
    
    const filteredStyles = stylesToRender.filter(style => 
      style.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredStyles.length === 0) {
      return <div className="p-8 text-center text-muted-foreground">
        <div className="mb-2">No styles found for "{searchTerm}" in this category.</div>
        <Button variant="ghost" size="sm" onClick={() => setSearchTerm("")}>Clear Search</Button>
      </div>;
    }

    return (
      <div className="divide-y border rounded-md font-generator-results bg-background/50 backdrop-blur-sm shadow-inner">
        {filteredStyles.map((font) => {
          const transformedText = font.transform(inputText);
          const isCopied = copiedStates[font.name] || false;
          return (
            <div key={font.name} className="flex items-center justify-between p-4 hover:bg-accent/30 transition-colors">
              <div className="flex-1">
                <div className="text-xs font-medium text-muted-foreground mb-2">{font.name}</div>
                <div className="text-xl break-all">{transformedText}</div>
              </div>
              <div className="flex gap-2 ml-4">
                <Button
                  size="sm"
                  variant={isCopied ? "default" : "outline"}
                  className={`h-9 gap-1.5 fancy-button w-[90px] ${isCopied ? "bg-primary text-primary-foreground" : "border-primary/30"}`}
                  onClick={() => handleCopy(transformedText, font.name)}
                  aria-label={`Copy ${font.name} style text: ${transformedText}`}
                >
                  {isCopied ? (
                    <>
                      <Check className="h-3.5 w-3.5" />
                      <span>Copied!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-3.5 w-3.5" />
                      <span>Copy</span>
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-9 gap-1.5 w-[90px] border-blue-500/30 text-blue-600 hover:bg-blue-50"
                  onClick={() => handlePreview(transformedText, font.name)}
                  aria-label={`Preview ${font.name} style text: ${transformedText}`}
                >
                  <Eye className="h-3.5 w-3.5" />
                  <span>Preview</span>
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <section id="font-generator" className="py-12 md:py-24 relative overflow-hidden">
      <div className="absolute -top-20 -left-20 w-64 h-64 bg-purple-600/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 right-10 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"></div>
      
      <div className="container px-4 md:px-6 relative">
         <div className="text-center mb-10">
           <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-center mb-4 gradient-text">
             Cursive & Tattoo Font Generator
           </h2>
           <p className="text-xl opacity-90 max-w-3xl mx-auto">
             Transform any text into dozens of stylish fonts instantly. 
             One click to copy, paste anywhere.
           </p>
         </div>

        <Card className="max-w-4xl mx-auto border shadow-lg bg-card/80 backdrop-blur-sm font-card transform transition-all hover:translate-y-[-5px] duration-500">
          <CardHeader className="border-b bg-muted/30">
            <CardTitle className="flex items-center gap-2">
              <Wand className="h-5 w-5 text-primary" />
              <span className="gradient-text">Text Transformer</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
                              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label htmlFor="text-input" className="text-sm font-medium">Enter your text here</label>
                  <Button variant="ghost" size="sm" className="h-8 text-xs fancy-button" onClick={() => setInputText("")}>
                    <RefreshCcw className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                </div>
                <Input
                  id="text-input"
                  placeholder="Type something..."
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="w-full p-4 text-base border-2 border-primary/20 focus:border-primary/50 focus:ring-2 focus:ring-primary/20"
                  aria-label="Enter text to convert to stylish fonts"
                  aria-describedby="text-input-description"
                />
                <p id="text-input-description" className="text-xs text-muted-foreground sr-only">
                  Type any text to see it converted into various stylish font styles. Click copy button to copy your preferred style.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="text-lg font-semibold gradient-text">Generated Styles:</div>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search styles..."
                      className="w-full pl-9 h-9"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                <Tabs defaultValue="cursive" value={activeCategory} onValueChange={setActiveCategory} className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="cursive">Cursive</TabsTrigger>
                    <TabsTrigger value="tattoo">Tattoo</TabsTrigger>
                    <TabsTrigger value="all">All Styles</TabsTrigger>
                  </TabsList>
                  <TabsContent value="cursive" className="mt-4">
                    {renderFontList('cursive')}
                    <div className="mt-6 text-center">
                      <a
                        href="/cursive-fonts"
                        className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                      >
                        View All Cursive Fonts →
                      </a>
                    </div>
                  </TabsContent>
                  <TabsContent value="tattoo" className="mt-4">
                    {renderFontList('tattoo')}
                    <div className="mt-6 text-center">
                      <a
                        href="/tattoo-fonts"
                        className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                      >
                        View All Tattoo Fonts →
                      </a>
                    </div>
                  </TabsContent>
                  <TabsContent value="all" className="mt-4">{renderFontList('all')}</TabsContent>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default FontGenerator;