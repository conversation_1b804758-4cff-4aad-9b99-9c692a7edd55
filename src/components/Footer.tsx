import { Heart } from "lucide-react"

const Footer = () => {
  const year = new Date().getFullYear()
  
  return (
    <footer className="border-t py-8 md:py-12 bg-muted/30">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center gap-6 text-center">
          <div className="flex items-center justify-center gap-2">
            <Heart className="h-5 w-5 text-primary animate-pulse-glow" />
            <span className="text-xl font-bold gradient-text">Cursive Font Generator</span>
          </div>
          <p className="max-w-md text-muted-foreground text-sm">
            Create beautiful stylish text for social media and messaging
          </p>
          
          {/* Core Cursive Font Types Section - Highest Priority */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h3 className="text-lg font-bold text-primary mb-2">⭐ Cursive Font Types</h3>
              <p className="text-sm text-muted-foreground">Discover our complete collection of cursive fonts</p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-sm max-w-4xl mx-auto">
              <div className="space-y-3 text-center">
                <h4 className="font-semibold text-foreground border-b border-primary/20 pb-1">Elegant Cursive</h4>
                <div className="space-y-2">
                  <a href="/elegant-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Elegant Cursive Fonts
                  </a>
                  <a href="/sophisticated-script-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Sophisticated Script
                  </a>
                  <a href="/formal-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Formal Cursive
                  </a>
                </div>
              </div>

              <div className="space-y-3 text-center">
                <h4 className="font-semibold text-foreground border-b border-primary/20 pb-1">Modern Cursive</h4>
                <div className="space-y-2">
                  <a href="/modern-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Modern Cursive Fonts
                  </a>
                  <a href="/contemporary-script-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Contemporary Script
                  </a>
                  <a href="/trendy-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Trendy Cursive
                  </a>
                </div>
              </div>

              <div className="space-y-3 text-center">
                <h4 className="font-semibold text-foreground border-b border-primary/20 pb-1">Vintage Cursive</h4>
                <div className="space-y-2">
                  <a href="/vintage-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Vintage Cursive Fonts
                  </a>
                  <a href="/retro-script-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Retro Script
                  </a>
                  <a href="/classic-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Classic Cursive
                  </a>
                </div>
              </div>

              <div className="space-y-3 text-center">
                <h4 className="font-semibold text-foreground border-b border-primary/20 pb-1">Romantic Cursive</h4>
                <div className="space-y-2">
                  <a href="/romantic-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Romantic Cursive Fonts
                  </a>
                  <a href="/love-script-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Love Script
                  </a>
                  <a href="/wedding-cursive-fonts" className="block text-muted-foreground hover:text-primary transition-colors">
                    Wedding Cursive
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Supporting Content Sections */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-sm max-w-6xl">
            <div className="space-y-3">
              <h4 className="font-semibold text-foreground">Related Fonts</h4>
              <div className="space-y-2">
                <a href="/script-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Script Fonts
                </a>
                <a href="/handwriting-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Handwriting Fonts
                </a>
                <a href="/calligraphy-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Calligraphy Fonts
                </a>
                <a href="/cursive-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  All Cursive Fonts
                </a>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-foreground">Other Styles</h4>
              <div className="space-y-2">
                <a href="/tattoo-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Tattoo Fonts
                </a>
                <a href="/gothic-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Gothic Fonts
                </a>
                <a href="/bold-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Bold Fonts
                </a>
                <a href="/italic-fonts" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Italic Fonts
                </a>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-foreground">Learning Hub</h4>
              <div className="space-y-2">
                <a href="/how-to-use-cursive-fonts-instagram" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Instagram Cursive Guide
                </a>
                <a href="/social-media-font-tips" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Social Media Tips
                </a>
                <a href="/copy-paste-fonts-tutorial" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Copy & Paste Tutorial
                </a>
                <a href="/cursive-font-psychology" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Cursive Font Psychology
                </a>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-foreground">Company Info</h4>
              <div className="space-y-2">
                <a href="/about" className="block text-muted-foreground hover:text-foreground transition-colors">
                  About Our Cursive Generator
                </a>
                <a href="/contact" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Contact Us
                </a>
                <a href="/privacy" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Privacy Policy
                </a>
                <a href="/terms" className="block text-muted-foreground hover:text-foreground transition-colors">
                  Terms of Service
                </a>
              </div>
            </div>
          </div>

          <div className="border-t pt-6 w-full text-center text-muted-foreground">
            <p className="text-sm">&copy; {year} Cursive Font Generator. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer