import { useState } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { But<PERSON> } from "@/components/ui/button"
import { She<PERSON>, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { Menu, Sparkles } from "lucide-react"

const Header = () => {
  const isMobile = useIsMobile()
  const [isOpen, setIsOpen] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  const handleNavigation = (path: string, sectionId?: string) => {
    setIsOpen(false)
    
    if (path === '/') {
      navigate('/')
      // If we're navigating to a section on home page, scroll after navigation
      if (sectionId) {
        setTimeout(() => {
          const section = document.getElementById(sectionId)
          if (section) {
            section.scrollIntoView({ behavior: 'smooth' })
          }
        }, 100)
      }
    } else {
      navigate(path)
    }
  }

  const handleHomeNavigation = () => {
    if (location.pathname === '/') {
      // If we're already on home page, just scroll to top
      window.scrollTo({top: 0, behavior: 'smooth'})
    } else {
      // If we're on another page, navigate to home
      navigate('/')
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/60 backdrop-blur-md">
      <div className="container flex h-16 items-center px-4 md:px-6">
        <div className="flex flex-1 items-center justify-between">
          <button 
            onClick={handleHomeNavigation}
            className="flex items-center gap-2 hover:opacity-80 transition-opacity"
          >
            <div className="bg-primary/10 p-1.5 rounded-lg">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <span className="text-xl font-bold tracking-tight gradient-text">Cursive Font Generator</span>
          </button>
          
          {isMobile ? (
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="ml-2">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle navigation</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right">
                <nav className="flex flex-col gap-4 mt-8">
                  <Button variant="ghost" className="justify-start" onClick={handleHomeNavigation}>
                    Home
                  </Button>

                  {/* Cursive Fonts - Core Category */}
                  <div className="text-sm font-semibold text-primary px-3 py-1">⭐ Cursive Fonts</div>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/', 'font-generator')}>
                    Cursive Font Generator
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/cursive-fonts')}>
                    All Cursive Fonts
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/elegant-cursive-fonts')}>
                    Elegant Cursive
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/modern-cursive-fonts')}>
                    Modern Cursive
                  </Button>

                  <hr className="my-2" />

                  {/* Related Fonts */}
                  <div className="text-sm font-semibold text-muted-foreground px-3 py-1">Related Fonts</div>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/script-fonts')}>
                    Script Fonts
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/handwriting-fonts')}>
                    Handwriting Fonts
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/calligraphy-fonts')}>
                    Calligraphy Fonts
                  </Button>

                  <hr className="my-2" />

                  {/* Other Styles */}
                  <div className="text-sm font-semibold text-muted-foreground px-3 py-1">Other Styles</div>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/tattoo-fonts')}>
                    Tattoo Fonts
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/gothic-fonts')}>
                    Gothic Fonts
                  </Button>

                  <hr className="my-2" />

                  {/* Tools & Features */}
                  <div className="text-sm font-semibold text-muted-foreground px-3 py-1">Tools & Features</div>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/', 'preview-section')}>
                    Live Preview
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/', 'symbol-library')}>
                    Symbol Library
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/', 'features')}>
                    All Features
                  </Button>

                  <hr className="my-2" />

                  {/* Learning Center */}
                  <div className="text-sm font-semibold text-muted-foreground px-3 py-1">Learning Center</div>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/how-to-use-cursive-fonts-instagram')}>
                    Instagram Cursive Guide
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/social-media-font-tips')}>
                    Social Media Tips
                  </Button>
                  <Button variant="ghost" className="justify-start pl-6" onClick={() => handleNavigation('/copy-paste-fonts-tutorial')}>
                    Copy & Paste Tutorial
                  </Button>

                  <hr className="my-2" />

                  {/* Company Info */}
                  <Button variant="ghost" className="justify-start" onClick={() => handleNavigation('/about')}>
                    About Us
                  </Button>
                  <Button variant="ghost" className="justify-start" onClick={() => handleNavigation('/contact')}>
                    Contact Us
                  </Button>
                </nav>
              </SheetContent>
            </Sheet>
          ) : (
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuLink 
                    className={cn(
                      "group inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50",
                      "cursor-pointer"
                    )}
                    onClick={handleHomeNavigation}
                  >
                    Home
                  </NavigationMenuLink>
                </NavigationMenuItem>
                
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Cursive Fonts ⭐</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[500px] gap-3 p-4">
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer border-l-4 border-primary"
                        onClick={() => handleNavigation('/', 'font-generator')}
                      >
                        <div className="font-medium leading-none mb-1">Cursive Font Generator</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Transform text into beautiful cursive fonts instantly
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/cursive-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">All Cursive Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Explore our complete cursive font collection
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/elegant-cursive-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Elegant Cursive Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Sophisticated and refined cursive styles
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/modern-cursive-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Modern Cursive Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Contemporary cursive fonts for modern designs
                        </div>
                      </NavigationMenuLink>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger>Related Fonts</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4">
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/script-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Script Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Flowing script fonts similar to cursive
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/handwriting-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Handwriting Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Natural handwritten font styles
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/calligraphy-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Calligraphy Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Artistic calligraphy and lettering fonts
                        </div>
                      </NavigationMenuLink>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger>Other Styles</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4">
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/tattoo-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Tattoo Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Bold fonts perfect for tattoo designs
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/gothic-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Gothic Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Dark and dramatic gothic font styles
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/bold-fonts')}
                      >
                        <div className="font-medium leading-none mb-1">Bold Fonts</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Strong and impactful bold font styles
                        </div>
                      </NavigationMenuLink>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger>Tools & Features</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4">
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/', 'preview-section')}
                      >
                        <div className="font-medium leading-none mb-1">Live Preview</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          See how your text will look on social media platforms
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/', 'symbol-library')}
                      >
                        <div className="font-medium leading-none mb-1">Symbol Library</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Browse and copy hundreds of special symbols
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/', 'features')}
                      >
                        <div className="font-medium leading-none mb-1">All Features</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Discover all our font generation features
                        </div>
                      </NavigationMenuLink>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger>Learning Center</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[400px] gap-3 p-4">
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/how-to-use-cursive-fonts-instagram')}
                      >
                        <div className="font-medium leading-none mb-1">Instagram Cursive Guide</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Master cursive fonts for Instagram success
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/social-media-font-tips')}
                      >
                        <div className="font-medium leading-none mb-1">Social Media Tips</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Font strategies across all platforms
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/copy-paste-fonts-tutorial')}
                      >
                        <div className="font-medium leading-none mb-1">Copy & Paste Tutorial</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Learn to copy fonts across devices
                        </div>
                      </NavigationMenuLink>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger>More</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid w-[300px] gap-3 p-4">
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/about')}
                      >
                        <div className="font-medium leading-none mb-1">About Us</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Learn more about our cursive font generator
                        </div>
                      </NavigationMenuLink>
                      <NavigationMenuLink
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground cursor-pointer"
                        onClick={() => handleNavigation('/contact')}
                      >
                        <div className="font-medium leading-none mb-1">Contact Us</div>
                        <div className="text-sm leading-snug text-muted-foreground">
                          Get in touch with questions or feedback
                        </div>
                      </NavigationMenuLink>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          )}
        </div>
        
        {!isMobile && (
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              className="ml-2 hidden md:flex fancy-button" 
              onClick={() => handleNavigation('/', 'font-generator')}
            >
              Get Started
            </Button>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header