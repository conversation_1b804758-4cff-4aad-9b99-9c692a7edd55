import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Lightbulb, Heart, Star, Palette, Camera, MessageCircle, Award, Sparkles } from 'lucide-react';

const FontInspirationShowcase: React.FC = () => {
  const showcaseItems = [
    {
      category: "Wedding & Romance",
      icon: Heart,
      color: "pink",
      examples: [
        {
          title: "Elegant Wedding Invitation",
          text: "𝒮𝒶𝓇𝒶𝒽 & 𝒥𝑜𝒽𝓃",
          subtitle: "𝒥𝓊𝓃𝑒 𝟣𝟧, 𝟤𝟢𝟤𝟦",
          description: "Cursive fonts create romantic elegance perfect for wedding stationery"
        },
        {
          title: "Anniversary Message",
          text: "𝐹𝑜𝓇𝑒𝓋𝑒𝓇 & 𝒜𝓁𝓌𝒶𝓎𝓈",
          subtitle: "ℒ𝑜𝓋𝑒 𝓎𝑜𝓊 𝓂𝑜𝓇𝑒 𝑒𝒶𝒸𝒽 𝒹𝒶𝓎",
          description: "Script fonts express deep emotions and personal connections"
        }
      ]
    },
    {
      category: "Business & Professional",
      icon: Award,
      color: "blue",
      examples: [
        {
          title: "Luxury Brand Identity",
          text: "ＰＲＥＭＩＵＭ ＣＯＬＬＥＣＴＩＯＮ",
          subtitle: "ᴇxᴄʟᴜsɪᴠᴇ ᴅᴇsɪɢɴ",
          description: "Small caps and wide fonts convey sophistication and exclusivity"
        },
        {
          title: "Professional Certificate",
          text: "𝕮𝖊𝖗𝖙𝖎𝖋𝖎𝖈𝖆𝖙𝖊 𝖔𝖋 𝕰𝖝𝖈𝖊𝖑𝖑𝖊𝖓𝖈𝖊",
          subtitle: "𝔞𝔴𝔞𝔯𝔡𝔢𝔡 𝔱𝔬",
          description: "Gothic fonts add gravitas and traditional authority"
        }
      ]
    },
    {
      category: "Social Media & Lifestyle",
      icon: Camera,
      color: "purple",
      examples: [
        {
          title: "Instagram Bio Style",
          text: "✨ 𝓛𝓲𝓿𝓲𝓷𝓰 𝓶𝔂 𝓫𝓮𝓼𝓽 𝓵𝓲𝓯𝓮 ✨",
          subtitle: "📍 ᴄᴀʟɪꜰᴏʀɴɪᴀ ᴅʀᴇᴀᴍɪɴɢ",
          description: "Mix of cursive and small caps creates engaging social media presence"
        },
        {
          title: "Motivational Quote",
          text: "𝗕𝗲 𝗬𝗼𝘂𝗿 𝗢𝘄𝗻 𝗞𝗶𝗻𝗱 𝗼𝗳 𝗕𝗲𝗮𝘂𝘁𝗶𝗳𝘂𝗹",
          subtitle: "𝘦𝘮𝘣𝘳𝘢𝘤𝘦 𝘺𝘰𝘶𝘳 𝘶𝘯𝘪𝘲𝘶𝘦 𝘫𝘰𝘶𝘳𝘯𝘦𝘺",
          description: "Bold and italic fonts emphasize empowerment and inspiration"
        }
      ]
    },
    {
      category: "Creative & Artistic",
      icon: Palette,
      color: "green",
      examples: [
        {
          title: "Retro Gaming Style",
          text: "ＧＡＭＥ ＯＶＥＲ",
          subtitle: "ᴘʀᴇss sᴛᴀʀᴛ ᴛᴏ ᴄᴏɴᴛɪɴᴜᴇ",
          description: "Vaporwave fonts evoke 80s nostalgia and gaming culture"
        },
        {
          title: "Artistic Portfolio",
          text: "𝒞𝓇𝑒𝒶𝓉𝒾𝓋𝑒 𝒱𝒾𝓈𝒾𝑜𝓃𝓈",
          subtitle: "𝔞 𝔧𝔬𝔲𝔯𝔫𝔢𝔶 𝔦𝔫 𝔞𝔯𝔱",
          description: "Elegant scripts showcase artistic sophistication and creativity"
        }
      ]
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      pink: "from-pink-50 to-rose-50 border-pink-200",
      blue: "from-blue-50 to-indigo-50 border-blue-200",
      purple: "from-purple-50 to-violet-50 border-purple-200",
      green: "from-green-50 to-emerald-50 border-green-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      pink: "text-pink-500",
      blue: "text-blue-500",
      purple: "text-purple-500",
      green: "text-green-500"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Font Inspiration Showcase: Creative Typography Examples & Ideas 2024"
        description="Discover stunning font combinations and typography inspiration. Explore real-world examples of cursive, bold, gothic, and decorative fonts in action."
        keywords="font inspiration, typography examples, font combinations, creative fonts, font showcase, typography design, font ideas"
        canonical="https://cursivefontgenerator.com/font-inspiration-showcase"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Lightbulb className="h-8 w-8 text-yellow-500 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold gradient-text">
                  Font Inspiration Showcase
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Discover endless possibilities with our curated collection of font combinations and real-world 
                typography examples. From elegant wedding invitations to bold social media posts.
              </p>
            </div>
          </div>
        </section>

        {/* Featured Examples */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto space-y-12">
              
              {showcaseItems.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center mb-4">
                      <category.icon className={`h-6 w-6 mr-3 ${getIconColor(category.color)}`} />
                      <h2 className="text-2xl font-bold">{category.category}</h2>
                    </div>
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    {category.examples.map((example, exampleIndex) => (
                      <Card key={exampleIndex} className={`bg-gradient-to-br ${getColorClasses(category.color)} border-2`}>
                        <CardContent className="p-8">
                          <div className="text-center mb-6">
                            <h3 className="text-lg font-semibold mb-4">{example.title}</h3>
                            <div className="bg-white p-6 rounded-lg shadow-sm mb-4">
                              <p className="text-2xl md:text-3xl mb-2">{example.text}</p>
                              <p className="text-lg text-gray-600">{example.subtitle}</p>
                            </div>
                            <p className="text-sm text-gray-700">{example.description}</p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Typography Trends */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 text-center flex items-center justify-center">
                    <Sparkles className="h-6 w-6 mr-3 text-yellow-500" />
                    Current Typography Trends
                  </h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">Popular Combinations</h3>
                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Elegant + Modern</h4>
                          <p className="text-sm text-gray-600 mb-2">𝒮𝒸𝓇𝒾𝓅𝓉 + ＳＡＮＳ ＳＥＲＩＦ</p>
                          <p className="text-xs text-gray-500">Perfect for luxury brands and premium products</p>
                        </div>
                        
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Bold + Minimal</h4>
                          <p className="text-sm text-gray-600 mb-2">𝗕𝗼𝗹𝗱 + ᴍɪɴɪᴍᴀʟ</p>
                          <p className="text-xs text-gray-500">Great for modern tech and startup branding</p>
                        </div>
                        
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Retro + Futuristic</h4>
                          <p className="text-sm text-gray-600 mb-2">ＶＡＰＯＲＷＡＶＥ + 𝘧𝘶𝘵𝘶𝘳𝘦</p>
                          <p className="text-xs text-gray-500">Popular in gaming and entertainment industries</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold mb-4">Industry Applications</h3>
                      <div className="space-y-4">
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Fashion & Beauty</h4>
                          <p className="text-sm text-gray-600">Elegant scripts and minimalist sans-serif combinations</p>
                        </div>
                        
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Food & Beverage</h4>
                          <p className="text-sm text-gray-600">Handwriting fonts and rustic script styles</p>
                        </div>
                        
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Technology</h4>
                          <p className="text-sm text-gray-600">Clean sans-serif with selective bold emphasis</p>
                        </div>
                        
                        <div className="bg-white p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Entertainment</h4>
                          <p className="text-sm text-gray-600">Gothic, decorative, and themed specialty fonts</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Design Tips */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 text-center">Professional Design Tips</h2>
                  
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Star className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold mb-3">Hierarchy Matters</h3>
                      <p className="text-sm text-gray-600">
                        Use different font weights and styles to create clear visual hierarchy. 
                        Bold for headlines, regular for body, italic for emphasis.
                      </p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Heart className="h-8 w-8 text-green-600" />
                      </div>
                      <h3 className="text-lg font-semibold mb-3">Emotional Connection</h3>
                      <p className="text-sm text-gray-600">
                        Choose fonts that match your message's emotional tone. 
                        Elegant scripts for romance, bold fonts for energy, clean fonts for trust.
                      </p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <MessageCircle className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="text-lg font-semibold mb-3">Readability First</h3>
                      <p className="text-sm text-gray-600">
                        No matter how beautiful a font is, it must be readable. 
                        Test your fonts at different sizes and on various devices.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Interactive Gallery */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-indigo-50 to-purple-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-6">Create Your Own Inspiration</h2>
              <p className="text-lg text-gray-600 mb-8">
                Ready to create your own stunning typography? Try our font generators and bring your creative vision to life.
              </p>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a 
                  href="/cursive-fonts" 
                  className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">𝒞𝓊𝓇𝓈𝒾𝓋𝑒</div>
                  <p className="text-sm text-gray-600">Elegant Scripts</p>
                </a>
                
                <a 
                  href="/bold-fonts" 
                  className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">𝗕𝗼𝗹𝗱</div>
                  <p className="text-sm text-gray-600">Strong Impact</p>
                </a>
                
                <a 
                  href="/gothic-fonts" 
                  className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">𝔊𝔬𝔱𝔥𝔦𝔠</div>
                  <p className="text-sm text-gray-600">Medieval Style</p>
                </a>
                
                <a 
                  href="/vaporwave-fonts" 
                  className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center group"
                >
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">ＲＥＴＲＯ</div>
                  <p className="text-sm text-gray-600">80s Aesthetic</p>
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card className="bg-gradient-to-r from-orange-500 to-pink-500 text-white">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-bold mb-4">Get Inspired and Start Creating</h2>
                  <p className="text-lg mb-6 opacity-90">
                    Join thousands of creators who use our font generators to bring their ideas to life. 
                    Start your typography journey today!
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a 
                      href="/" 
                      className="inline-flex items-center px-6 py-3 bg-white text-orange-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <Sparkles className="h-5 w-5 mr-2" />
                      Start Creating Now
                    </a>
                    <a 
                      href="/social-media-font-tips" 
                      className="inline-flex items-center px-6 py-3 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-colors"
                    >
                      <Lightbulb className="h-5 w-5 mr-2" />
                      Learn More Tips
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FontInspirationShowcase;
