import React, { useState } from 'react';
import { useLocation, Navigate } from 'react-router-dom';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import FontDetailGenerator from "@/components/FontDetailGenerator";
import FontCategoryContent from "@/components/FontCategoryContent";
import FontCategoryFAQ from "@/components/FontCategoryFAQ";
import RelatedFonts from "@/components/RelatedFonts";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent } from "@/components/ui/card";
import { fontStyles } from "@/lib/font-data";
import { FONT_CATEGORIES, CategoryKey } from "@/config/font-categories";

const FontDetailPage: React.FC = () => {
  const location = useLocation();
  const [previewText, setPreviewText] = useState("Your stylish text here");
  const [previewStyle, setPreviewStyle] = useState("script");

  // 从路径推断分类
  const category = location.pathname.slice(1); // 移除开头的 '/'

  // 验证分类是否存在
  if (!category || !(category in FONT_CATEGORIES)) {
    return <Navigate to="/404" replace />;
  }

  const categoryKey = category as CategoryKey;
  const categoryConfig = FONT_CATEGORIES[categoryKey];

  // 获取该分类的字体
  const categoryFonts = fontStyles.filter(font =>
    font.category === categoryConfig.category
  );

  // 处理预览功能
  const handlePreview = (text: string, styleName: string) => {
    setPreviewText(text);
    
    // 映射字体样式名称到预览样式
    const styleMapping: Record<string, string> = {
      "Cursive": "script",
      "Cursive Bold": "script", 
      "Elegant Script": "script",
      "Handwriting Style": "script",
      "Calligraphy Script": "script",
      "Gothic Cursive": "medieval",
      "Old English Script": "medieval",
      "Tattoo Script": "medieval",
      "Bold": "bold",
      "Italic": "italic",
      "Small Caps": "small",
      "Vaporwave": "vaporwave"
    };
    
    setPreviewStyle(styleMapping[styleName] || "script");
  };

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": categoryConfig.title,
    "description": categoryConfig.description,
    "url": `https://cursivefontgenerator.top/${category}`,
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "All",
    "browserRequirements": "Requires JavaScript and modern browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Font generation",
      "One-click copy function",
      "Real-time preview",
      "Mobile support"
    ]
  };

  return (
    <div className="flex min-h-screen flex-col">
      <SEOHead 
        title={categoryConfig.seo.title}
        description={categoryConfig.seo.description}
        keywords={categoryConfig.seo.keywords}
        structuredData={structuredData}
        canonical={`https://cursivefontgenerator.top/${category}`}
      />
      
      <Header />
      
      <main className="flex-1">
        {/* 面包屑导航 */}
        <section className="py-4 bg-gray-50 border-b">
          <div className="container px-4 md:px-6">
            <Breadcrumb categoryKey={categoryKey} />
          </div>
        </section>

        {/* 页面标题区域 */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 gradient-text">
                {categoryConfig.title}
              </h1>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                {categoryConfig.description}
              </p>
            </div>
          </div>
        </section>

        {/* 字体生成器区域 */}
        <FontDetailGenerator
          fonts={categoryFonts}
          category={categoryKey}
          title={categoryConfig.title}
          description={`Professional ${categoryConfig.category} font generation tool`}
          onPreview={handlePreview}
        />

        {/* 详细内容介绍区域 */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              {/* 主要介绍 */}
              <Card>
                <CardContent className="p-8">
                  <FontCategoryContent categoryKey={categoryKey} />
                </CardContent>
              </Card>

              {/* 使用指南 */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Step-by-Step Usage Guide</h2>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">Getting Started</h3>
                      <ol className="list-decimal pl-6 space-y-3">
                        <li className="leading-relaxed">
                          <strong>Enter Your Text:</strong> Type or paste your desired text into the input field above.
                        </li>
                        <li className="leading-relaxed">
                          <strong>Browse Font Styles:</strong> Scroll through our curated collection of {categoryConfig.category} fonts.
                        </li>
                        <li className="leading-relaxed">
                          <strong>Copy Your Choice:</strong> Click the "Copy" button next to your preferred style.
                        </li>
                        <li className="leading-relaxed">
                          <strong>Paste and Use:</strong> Paste your stylized text anywhere that supports Unicode text.
                        </li>
                      </ol>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-4">Pro Tips</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                          <span className="leading-relaxed">Test your fonts on the target platform first</span>
                        </li>
                        <li className="flex items-start">
                          <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                          <span className="leading-relaxed">Use the search function to quickly find specific styles</span>
                        </li>
                        <li className="flex items-start">
                          <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                          <span className="leading-relaxed">Keep your text concise for better readability</span>
                        </li>
                        <li className="flex items-start">
                          <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                          <span className="leading-relaxed">Bookmark this page for quick access</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 技术信息和兼容性 */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Technical Information & Compatibility</h2>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">Unicode Technology</h3>
                      <p className="mb-4 leading-relaxed">
                        Our {categoryConfig.category} font generator uses advanced Unicode character mapping to create stylized text.
                        Unlike image-based fonts, Unicode fonts maintain all text properties including searchability,
                        selectability, and accessibility features.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold mb-4">Platform Compatibility</h3>
                      <div className="grid md:grid-cols-3 gap-4">
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-green-800 mb-2">✓ Fully Supported</h4>
                          <ul className="text-sm text-green-700 space-y-1">
                            <li>Instagram, Facebook, Twitter</li>
                            <li>WhatsApp, Telegram, Discord</li>
                            <li>Google Docs, Microsoft Word</li>
                            <li>Most modern web browsers</li>
                          </ul>
                        </div>
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-yellow-800 mb-2">⚠ Partial Support</h4>
                          <ul className="text-sm text-yellow-700 space-y-1">
                            <li>Some email clients</li>
                            <li>Older mobile devices</li>
                            <li>Legacy software applications</li>
                            <li>Basic text editors</li>
                          </ul>
                        </div>
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-red-800 mb-2">✗ Limited Support</h4>
                          <ul className="text-sm text-red-700 space-y-1">
                            <li>SMS text messages</li>
                            <li>Command line interfaces</li>
                            <li>Very old browsers</li>
                            <li>Plain text environments</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* FAQ 部分 */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
                  <FontCategoryFAQ categoryKey={categoryKey} />

                  {/* 通用FAQ */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <h3 className="text-xl font-semibold mb-4">General Questions</h3>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-lg font-semibold mb-2">Are these fonts free to use?</h4>
                        <p className="text-gray-700">Yes, all our Unicode fonts are completely free for personal and commercial use.</p>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold mb-2">Do I need to download anything?</h4>
                        <p className="text-gray-700">No downloads required. Simply copy and paste the generated text anywhere you need it.</p>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold mb-2">Will these fonts work on mobile devices?</h4>
                        <p className="text-gray-700">Yes, Unicode fonts work on all modern mobile devices and operating systems.</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* 相关字体推荐 */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <RelatedFonts currentCategory={categoryKey} />
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default FontDetailPage;
