import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import FontDetailPage from '../FontDetailPage'

// Mock components
vi.mock('@/components/Header', () => ({
  default: () => <div data-testid="header">Header</div>,
}))

vi.mock('@/components/Footer', () => ({
  default: () => <div data-testid="footer">Footer</div>,
}))

vi.mock('@/components/SEOHead', () => ({
  default: ({ title, description }: { title: string; description: string }) => (
    <div data-testid="seo-head">
      <div data-testid="seo-title">{title}</div>
      <div data-testid="seo-description">{description}</div>
    </div>
  ),
}))

vi.mock('@/components/FontDetailGenerator', () => ({
  default: ({ title, category }: { title: string; category: string }) => (
    <div data-testid="font-generator">
      <div data-testid="generator-title">{title}</div>
      <div data-testid="generator-category">{category}</div>
    </div>
  ),
}))

// Mock font data
vi.mock('@/lib/font-data', () => ({
  fontStyles: [
    {
      name: 'Cursive',
      category: 'cursive',
      description: 'Classic cursive font',
      transform: (text: string) => `cursive-${text}`,
    },
    {
      name: 'Gothic',
      category: 'tattoo',
      description: 'Gothic font',
      transform: (text: string) => `gothic-${text}`,
    },
  ],
}))

const renderWithRouter = (initialEntries: string[]) => {
  return render(
    <MemoryRouter initialEntries={initialEntries}>
      <FontDetailPage />
    </MemoryRouter>
  )
}

describe('FontDetailPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Cursive Fonts Page', () => {
    it('renders cursive fonts page correctly', () => {
      renderWithRouter(['/cursive-fonts'])

      expect(screen.getByTestId('header')).toBeInTheDocument()
      expect(screen.getByTestId('footer')).toBeInTheDocument()
      expect(screen.getAllByText('Free Cursive Font Generator')).toHaveLength(2) // H1 and generator title
    })

    it('sets correct SEO metadata for cursive fonts', () => {
      renderWithRouter(['/cursive-fonts'])

      expect(screen.getByTestId('seo-title')).toHaveTextContent(
        'Free Cursive Font Generator - Elegant Script Online Converter | Cursive Font Generator'
      )
      expect(screen.getByTestId('seo-description')).toHaveTextContent(
        'Professional cursive font generator with multiple elegant script styles'
      )
    })

    it('renders cursive-specific content sections', () => {
      renderWithRouter(['/cursive-fonts'])

      // Check for main content sections by their headings
      expect(screen.getByText('Complete Guide to Cursive Fonts')).toBeInTheDocument()
      expect(screen.getByText('What Are Cursive Fonts?')).toBeInTheDocument()
      expect(screen.getByText('Step-by-Step Usage Guide')).toBeInTheDocument()
      expect(screen.getByText('Technical Information & Compatibility')).toBeInTheDocument()
    })

    it('passes correct props to FontDetailGenerator', () => {
      renderWithRouter(['/cursive-fonts'])

      expect(screen.getByTestId('generator-title')).toHaveTextContent('Free Cursive Font Generator')
      expect(screen.getByTestId('generator-category')).toHaveTextContent('cursive-fonts')
    })
  })

  describe('Tattoo Fonts Page', () => {
    it('renders tattoo fonts page correctly', () => {
      renderWithRouter(['/tattoo-fonts'])

      expect(screen.getByTestId('header')).toBeInTheDocument()
      expect(screen.getByTestId('footer')).toBeInTheDocument()
      expect(screen.getAllByText('Free Tattoo Font Generator')).toHaveLength(2) // H1 and generator title
    })

    it('sets correct SEO metadata for tattoo fonts', () => {
      renderWithRouter(['/tattoo-fonts'])

      expect(screen.getByTestId('seo-title')).toHaveTextContent(
        'Free Tattoo Font Generator - Gothic Old English Online Converter | Tattoo Font Generator'
      )
      expect(screen.getByTestId('seo-description')).toHaveTextContent(
        'Professional tattoo font generator with Gothic, Old English and other tattoo font styles'
      )
    })

    it('renders tattoo-specific content sections', () => {
      renderWithRouter(['/tattoo-fonts'])

      // Check for main content sections by their headings
      expect(screen.getByText('Complete Guide to Tattoo Fonts')).toBeInTheDocument()
      expect(screen.getByText('What Are Tattoo Fonts?')).toBeInTheDocument()
      expect(screen.getByText('Step-by-Step Usage Guide')).toBeInTheDocument()
      expect(screen.getByText('Technical Information & Compatibility')).toBeInTheDocument()
    })

    it('passes correct props to FontDetailGenerator', () => {
      renderWithRouter(['/tattoo-fonts'])

      expect(screen.getByTestId('generator-title')).toHaveTextContent('Free Tattoo Font Generator')
      expect(screen.getByTestId('generator-category')).toHaveTextContent('tattoo-fonts')
    })
  })

  describe('Invalid Category', () => {
    it('redirects to 404 for invalid category', () => {
      renderWithRouter(['/invalid-fonts'])
      
      // Since we're using Navigate to="/404", the component should not render the main content
      expect(screen.queryByTestId('header')).not.toBeInTheDocument()
      expect(screen.queryByText('Free Cursive Font Generator')).not.toBeInTheDocument()
    })
  })

  describe('Usage Instructions', () => {
    it('displays usage guide sections', () => {
      renderWithRouter(['/cursive-fonts'])

      expect(screen.getByText('Step-by-Step Usage Guide')).toBeInTheDocument()
      expect(screen.getByText('Getting Started')).toBeInTheDocument()
      expect(screen.getByText('Pro Tips')).toBeInTheDocument()
    })

    it('displays technical information section', () => {
      renderWithRouter(['/cursive-fonts'])

      expect(screen.getByText('Technical Information & Compatibility')).toBeInTheDocument()
      expect(screen.getByText('Unicode Technology')).toBeInTheDocument()
      expect(screen.getByText('Platform Compatibility')).toBeInTheDocument()
    })
  })

  describe('SEO and Metadata', () => {
    it('includes SEO head component', () => {
      renderWithRouter(['/cursive-fonts'])

      // The SEO head component should be present
      expect(screen.getByTestId('seo-head')).toBeInTheDocument()
    })

    it('renders FAQ section for cursive fonts', () => {
      renderWithRouter(['/cursive-fonts'])

      expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument()
    })
  })
})
