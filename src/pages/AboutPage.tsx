import { <PERSON>, <PERSON>rk<PERSON>, Users, Globe, Target, Award, Zap, Shield } from "lucide-react"
import PageLayout from "@/components/PageLayout"
import SEOHead from "@/components/SEOHead"
import { Card, CardContent } from "@/components/ui/card"

const AboutPage = () => {
  return (
    <>
      <SEOHead
        title="About Us - Cursive Font Generator"
        description="Learn about Cursive Font Generator, the leading online text styling tool for creators. Discover our mission to make beautiful font effects accessible to everyone."
        canonical="https://cursivefontgenerator.top/about"
        keywords="about cursive font generator, text styling tool, font creator, online typography"
      />
      <PageLayout
        title="About Cursive Font Generator"
        breadcrumbs={[{ label: "About Us" }]}
      >
      <div className="max-w-4xl mx-auto space-y-12">
        {/* Introduction */}
        <div className="text-center space-y-6">
          <div className="space-y-4">
            <p className="text-xl text-muted-foreground leading-relaxed">
              Cursive Font Generator is the leading online text styling platform designed for creators,
              content makers, and anyone who wants to add personality to their digital text.
            </p>
            <p className="text-lg text-muted-foreground leading-relaxed">
              We are dedicated to providing the simplest and most user-friendly text conversion service,
              enabling everyone to easily create unique and beautiful text effects for social media,
              design projects, and personal expression.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">50+</div>
              <div className="text-sm text-muted-foreground">Font Styles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">1M+</div>
              <div className="text-sm text-muted-foreground">Monthly Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">100%</div>
              <div className="text-sm text-muted-foreground">Free to Use</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">24/7</div>
              <div className="text-sm text-muted-foreground">Available</div>
            </div>
          </div>
        </div>

        {/* Core Features */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Why Choose Our Font Generator?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We've built the most comprehensive and user-friendly font styling platform
              with features that make text transformation effortless and enjoyable.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <Sparkles className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">50+ Font Styles</h3>
                <p className="text-sm text-muted-foreground">
                  From elegant cursive to bold tattoo fonts, discover a rich variety of styles for every occasion
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">Instant Results</h3>
                <p className="text-sm text-muted-foreground">
                  Real-time text transformation with one-click copy functionality for immediate use
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">User-Centric Design</h3>
                <p className="text-sm text-muted-foreground">
                  Clean, intuitive interface designed for creators of all skill levels
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">Universal Compatibility</h3>
                <p className="text-sm text-muted-foreground">
                  Works seamlessly across all social media platforms and messaging apps
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8">
          <Card className="p-8">
            <CardContent className="space-y-4">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Target className="h-6 w-6 text-primary" />
              </div>
              <h2 className="text-2xl font-bold">Our Mission</h2>
              <p className="text-muted-foreground leading-relaxed">
                To democratize beautiful typography by making unique text effects accessible to everyone.
                We believe that creative expression through text should be simple, free, and available
                to creators worldwide, regardless of their technical expertise.
              </p>
            </CardContent>
          </Card>

          <Card className="p-8">
            <CardContent className="space-y-4">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Award className="h-6 w-6 text-primary" />
              </div>
              <h2 className="text-2xl font-bold">Our Vision</h2>
              <p className="text-muted-foreground leading-relaxed">
                To become the world's leading platform for text styling and typography tools,
                empowering millions of creators to enhance their digital content with beautiful,
                expressive fonts that capture attention and convey personality.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="bg-muted/50 rounded-lg p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-4">Our Core Values</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              These principles guide everything we do and shape how we serve our community of creators.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center space-y-3">
              <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <Heart className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">Simplicity First</h3>
              <p className="text-sm text-muted-foreground">
                We prioritize ease of use and intuitive design in everything we create.
              </p>
            </div>

            <div className="text-center space-y-3">
              <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">Privacy Focused</h3>
              <p className="text-sm text-muted-foreground">
                Your text and data remain private. We don't store your input content.
              </p>
            </div>

            <div className="text-center space-y-3">
              <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">Innovation Driven</h3>
              <p className="text-sm text-muted-foreground">
                We continuously improve and add new features based on user feedback.
              </p>
            </div>
          </div>
        </div>

        {/* Use Cases */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Perfect For</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our font generator serves a diverse community of creators and professionals across various fields.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="p-6">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Social Media Creators</h3>
                <p className="text-sm text-muted-foreground">
                  Stand out on Instagram, Twitter, TikTok, and other platforms with eye-catching text styles.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Content Marketers</h3>
                <p className="text-sm text-muted-foreground">
                  Create engaging posts and captions that capture attention and drive engagement.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Graphic Designers</h3>
                <p className="text-sm text-muted-foreground">
                  Find inspiration and create unique text elements for design projects.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Students & Educators</h3>
                <p className="text-sm text-muted-foreground">
                  Make presentations and assignments more visually appealing and memorable.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Small Business Owners</h3>
                <p className="text-sm text-muted-foreground">
                  Create professional-looking marketing materials without expensive design software.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Personal Use</h3>
                <p className="text-sm text-muted-foreground">
                  Add personality to messages, invitations, and personal creative projects.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Technology & Innovation */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Technology & Innovation</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Behind our simple interface lies sophisticated technology that makes beautiful typography accessible to everyone.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="p-6">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <Sparkles className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Unicode Technology</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Our font generator uses advanced Unicode character mapping to create stylized text that maintains
                  all text properties. Unlike image-based solutions, our fonts remain searchable, selectable,
                  and accessible across all platforms and devices.
                </p>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Cross-platform compatibility</li>
                  <li>• Maintains text properties</li>
                  <li>• Accessibility compliant</li>
                  <li>• SEO-friendly text</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <Award className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Performance Optimization</h3>
                <p className="text-muted-foreground leading-relaxed">
                  We've optimized every aspect of our platform for speed and efficiency. From instant text
                  transformation to lightning-fast copy operations, we ensure the best possible user experience
                  without compromising on quality or functionality.
                </p>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Sub-100ms text conversion</li>
                  <li>• Optimized for mobile devices</li>
                  <li>• Minimal resource usage</li>
                  <li>• Progressive web app features</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Our Story */}
        <div className="bg-muted/50 rounded-lg p-8">
          <div className="max-w-3xl mx-auto text-center space-y-6">
            <h2 className="text-2xl font-bold">Our Story</h2>
            <div className="space-y-4 text-muted-foreground leading-relaxed">
              <p>
                Cursive Font Generator was born from a simple observation: while beautiful typography has always
                been essential for effective communication, creating stylized text required expensive software
                or technical expertise that many people didn't have.
              </p>
              <p>
                We set out to democratize typography by creating a tool that anyone could use, regardless of
                their technical background or budget. What started as a simple text converter has evolved into
                a comprehensive platform serving millions of users worldwide.
              </p>
              <p>
                Today, our platform powers creative expression for social media influencers, small business owners,
                students, designers, and anyone who wants to make their text stand out. We're proud to be part
                of countless success stories, from viral social media posts to memorable brand identities.
              </p>
            </div>
          </div>
        </div>

        {/* Community Impact */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Community Impact</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We're proud of the positive impact our platform has had on creators and businesses worldwide.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-primary">1M+</div>
              <div className="text-sm text-muted-foreground">Monthly Active Users</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-primary">50M+</div>
              <div className="text-sm text-muted-foreground">Fonts Generated</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-primary">195</div>
              <div className="text-sm text-muted-foreground">Countries Served</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-primary">99.9%</div>
              <div className="text-sm text-muted-foreground">Uptime</div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6 mt-8">
            <Card className="p-6 text-center">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Small Businesses</h3>
                <p className="text-sm text-muted-foreground">
                  Helping entrepreneurs create professional branding without expensive design costs
                </p>
              </CardContent>
            </Card>

            <Card className="p-6 text-center">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Content Creators</h3>
                <p className="text-sm text-muted-foreground">
                  Empowering influencers and creators to stand out on social media platforms
                </p>
              </CardContent>
            </Card>

            <Card className="p-6 text-center">
              <CardContent className="space-y-3">
                <h3 className="font-semibold">Students & Educators</h3>
                <p className="text-sm text-muted-foreground">
                  Making presentations and projects more engaging and visually appealing
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Future Vision */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Looking Forward</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We're constantly innovating and expanding our platform to serve our growing community better.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Upcoming Features</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="bg-primary/10 text-primary rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                  <div>
                    <div className="font-medium">Advanced Font Customization</div>
                    <div className="text-sm text-muted-foreground">Fine-tune spacing, sizing, and styling options</div>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-primary/10 text-primary rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                  <div>
                    <div className="font-medium">Collaboration Tools</div>
                    <div className="text-sm text-muted-foreground">Share and collaborate on font projects with teams</div>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-primary/10 text-primary rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                  <div>
                    <div className="font-medium">API Integration</div>
                    <div className="text-sm text-muted-foreground">Integrate our font generation into your applications</div>
                  </div>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Our Commitment</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">✓</span>
                  <div>
                    <div className="font-medium">Always Free Core Features</div>
                    <div className="text-sm text-muted-foreground">Basic font generation will always remain free</div>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">✓</span>
                  <div>
                    <div className="font-medium">Privacy First</div>
                    <div className="text-sm text-muted-foreground">Your data and text remain private and secure</div>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-green-100 text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">✓</span>
                  <div>
                    <div className="font-medium">Continuous Innovation</div>
                    <div className="text-sm text-muted-foreground">Regular updates and new features based on user feedback</div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="text-center space-y-6">
          <h2 className="text-2xl font-bold">Get in Touch</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We love hearing from our users! Whether you have questions, suggestions, or just want to share
            how you're using our font generator, we're here to help.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <a
              href="/contact"
              className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Contact Us
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 border border-primary text-primary px-6 py-3 rounded-lg hover:bg-primary/10 transition-colors"
            >
              Email Support
            </a>
          </div>
        </div>
      </div>
    </PageLayout>
    </>
  )
}

export default AboutPage 