import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Building2, Target, Users, Zap, Crown, Shield, Lightbulb, TrendingUp } from 'lucide-react';

const BrandFontSelectionGuide: React.FC = () => {
  const brandPersonalities = [
    {
      type: "Professional & Trustworthy",
      icon: Shield,
      color: "blue",
      description: "Financial services, healthcare, legal firms",
      fontExamples: ["𝖢𝗅𝖾𝖺𝗇 𝖲𝖺𝗇𝗌", "𝒞𝓁𝒶𝓈𝓈𝒾𝒸 𝒮𝑒𝓇𝒾𝒻", "𝗠𝗼𝗱𝗲𝗿𝗻"],
      characteristics: ["Reliability", "Stability", "Professionalism", "Authority"],
      industries: ["Banking", "Healthcare", "Law", "Insurance"]
    },
    {
      type: "Luxury & Premium",
      icon: Crown,
      color: "purple",
      description: "High-end brands, luxury goods, premium services",
      fontExamples: ["𝒮𝒸𝓇𝒾𝓅𝓉 ℰ𝓁𝑒𝑔𝒶𝓃𝒸𝑒", "𝔇𝔦𝔞𝔪𝔬𝔫𝔡", "ℌ𝔞𝔫𝔡𝔠𝔯𝔞𝔣𝔱"],
      characteristics: ["Exclusivity", "Sophistication", "Quality", "Heritage"],
      industries: ["Fashion", "Jewelry", "Automotive", "Hospitality"]
    },
    {
      type: "Creative & Innovative",
      icon: Lightbulb,
      color: "orange",
      description: "Tech startups, design agencies, creative studios",
      fontExamples: ["𝗜𝗻𝗻𝗼𝘃𝗮𝘁𝗶𝘃𝗲", "𝒞𝓇𝑒𝒶𝓉𝒾𝓋𝑒", "ＦＵＴＵＲＥ"],
      characteristics: ["Innovation", "Creativity", "Forward-thinking", "Uniqueness"],
      industries: ["Technology", "Design", "Startups", "Media"]
    },
    {
      type: "Friendly & Approachable",
      icon: Users,
      color: "green",
      description: "Consumer brands, education, community services",
      fontExamples: ["𝒻𝓇𝒾𝑒𝓃𝒹𝓁𝓎", "𝒶𝓅𝓅𝓇𝑜𝒶𝒸𝒽𝒶𝒷𝓁𝑒", "𝚌𝚊𝚜𝚞𝚊𝚕"],
      characteristics: ["Warmth", "Accessibility", "Community", "Trust"],
      industries: ["Education", "Retail", "Food & Beverage", "Non-profit"]
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "from-blue-50 to-indigo-50 border-blue-200",
      purple: "from-purple-50 to-violet-50 border-purple-200",
      orange: "from-orange-50 to-red-50 border-orange-200",
      green: "from-green-50 to-emerald-50 border-green-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "text-blue-500",
      purple: "text-purple-500",
      orange: "text-orange-500",
      green: "text-green-500"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Brand Font Selection Guide: Choose Perfect Typography for Your Business"
        description="Master brand typography with our comprehensive guide. Learn how to select fonts that align with your brand personality and business goals."
        keywords="brand fonts, brand typography, font selection, business fonts, brand identity, corporate fonts, font strategy"
        canonical="https://cursivefontgenerator.com/brand-font-selection-guide"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Building2 className="h-8 w-8 text-blue-400 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold">
                  Brand Font Selection Guide
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Master the art of brand typography. Learn how to choose fonts that perfectly 
                represent your brand personality, connect with your audience, and drive business success.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Why Brand Typography Matters</h2>
                  <div className="prose prose-lg max-w-none">
                    <p className="mb-4 leading-relaxed">
                      Typography is one of the most powerful tools in brand communication. Research shows that 
                      consumers form impressions about a brand within 50 milliseconds of exposure, and typography 
                      plays a crucial role in this rapid assessment. The right font choice can convey trust, 
                      innovation, luxury, or approachability before a single word is read.
                    </p>
                    <p className="mb-4 leading-relaxed">
                      Brand typography goes beyond aesthetics—it's a strategic business decision that affects 
                      customer perception, brand recognition, and ultimately, business performance. Companies 
                      with consistent brand presentation across all platforms see revenue increases of up to 23%.
                    </p>
                  </div>
                  
                  <div className="grid md:grid-cols-3 gap-6 mt-8">
                    <div className="text-center">
                      <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Target className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Brand Recognition</h3>
                      <p className="text-sm text-gray-600">Consistent typography increases brand recognition by 80%</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Users className="h-8 w-8 text-green-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Customer Trust</h3>
                      <p className="text-sm text-gray-600">Professional typography builds credibility and trust</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <TrendingUp className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Business Impact</h3>
                      <p className="text-sm text-gray-600">Strategic font choices can increase conversions by 38%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Brand Personalities */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto space-y-8">
              
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">Font Selection by Brand Personality</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Different brand personalities require different typographic approaches. 
                  Find the style that best represents your brand's unique character.
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {brandPersonalities.map((brand, index) => (
                  <Card key={index} className={`bg-gradient-to-br ${getColorClasses(brand.color)} border-2`}>
                    <CardContent className="p-8">
                      <div className="flex items-center mb-6">
                        <brand.icon className={`h-8 w-8 mr-4 ${getIconColor(brand.color)}`} />
                        <div>
                          <h3 className="text-xl font-bold">{brand.type}</h3>
                          <p className="text-gray-600">{brand.description}</p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-3">Font Examples:</h4>
                          <div className="bg-white p-4 rounded-lg space-y-2">
                            {brand.fontExamples.map((example, i) => (
                              <div key={i} className="text-lg">{example}</div>
                            ))}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold mb-2">Key Characteristics:</h4>
                            <div className="space-y-1">
                              {brand.characteristics.map((char, i) => (
                                <span key={i} className="block text-sm text-gray-700">• {char}</span>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">Common Industries:</h4>
                            <div className="flex flex-wrap gap-1">
                              {brand.industries.map((industry, i) => (
                                <span key={i} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                  {industry}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Selection Process */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">5-Step Brand Font Selection Process</h2>
                  
                  <div className="space-y-8">
                    <div className="border-l-4 border-blue-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                        Define Your Brand Personality
                      </h3>
                      <p className="text-gray-700 mb-4">
                        Start by clearly defining your brand's personality traits. Are you professional and 
                        trustworthy, or creative and innovative? Your font choice should reflect these core characteristics.
                      </p>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">Key Questions to Ask:</h4>
                        <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                          <li>What emotions do you want customers to feel about your brand?</li>
                          <li>How formal or casual is your brand voice?</li>
                          <li>What values does your brand represent?</li>
                          <li>How do you want to be perceived compared to competitors?</li>
                        </ul>
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                        Analyze Your Target Audience
                      </h3>
                      <p className="text-gray-700 mb-4">
                        Consider your audience's demographics, preferences, and expectations. Different age groups, 
                        cultures, and industries have varying typographic preferences and associations.
                      </p>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">Audience Considerations:</h4>
                        <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                          <li>Age demographics and generational preferences</li>
                          <li>Cultural background and reading habits</li>
                          <li>Industry expectations and conventions</li>
                          <li>Digital vs. print consumption patterns</li>
                        </ul>
                      </div>
                    </div>

                    <div className="border-l-4 border-purple-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                        Research Competitor Typography
                      </h3>
                      <p className="text-gray-700 mb-4">
                        Study your competitors' font choices to understand industry standards and identify 
                        opportunities for differentiation. Aim to stand out while remaining appropriate for your sector.
                      </p>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">Competitive Analysis:</h4>
                        <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                          <li>Identify common typographic patterns in your industry</li>
                          <li>Note successful brands that break conventional rules</li>
                          <li>Find gaps where your brand can differentiate</li>
                          <li>Ensure your choice doesn't confuse you with competitors</li>
                        </ul>
                      </div>
                    </div>

                    <div className="border-l-4 border-orange-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">4</span>
                        Test Across Applications
                      </h3>
                      <p className="text-gray-700 mb-4">
                        Test your font choices across all brand touchpoints—from business cards to billboards, 
                        from mobile apps to social media. Ensure consistency and readability at all sizes.
                      </p>
                      <div className="bg-orange-50 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">Testing Checklist:</h4>
                        <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                          <li>Logo and brand mark applications</li>
                          <li>Website and mobile interface readability</li>
                          <li>Print materials at various sizes</li>
                          <li>Social media and digital advertising</li>
                        </ul>
                      </div>
                    </div>

                    <div className="border-l-4 border-red-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">5</span>
                        Validate with Stakeholders
                      </h3>
                      <p className="text-gray-700 mb-4">
                        Get feedback from key stakeholders, including team members, customers, and brand partners. 
                        Ensure the chosen typography resonates with all important audiences.
                      </p>
                      <div className="bg-red-50 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">Validation Methods:</h4>
                        <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                          <li>Internal team reviews and feedback sessions</li>
                          <li>Customer surveys and focus groups</li>
                          <li>A/B testing in real applications</li>
                          <li>Professional design consultation</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Common Font Selection Mistakes</h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-red-600">❌ What to Avoid</h3>
                      <div className="space-y-4">
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Following Trends Blindly</h4>
                          <p className="text-sm text-red-700">
                            Choosing fonts based on current trends rather than brand appropriateness 
                            can make your brand feel dated quickly.
                          </p>
                        </div>
                        
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Ignoring Readability</h4>
                          <p className="text-sm text-red-700">
                            Prioritizing style over legibility can harm user experience and 
                            accessibility, especially in digital applications.
                          </p>
                        </div>
                        
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Inconsistent Application</h4>
                          <p className="text-sm text-red-700">
                            Using different fonts across touchpoints without a clear hierarchy 
                            weakens brand recognition and professionalism.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-green-600">✅ Best Practices</h3>
                      <div className="space-y-4">
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Strategic Alignment</h4>
                          <p className="text-sm text-green-700">
                            Choose fonts that strategically support your brand goals and 
                            resonate with your specific target audience.
                          </p>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Scalable Solutions</h4>
                          <p className="text-sm text-green-700">
                            Select fonts that work well across all applications, from 
                            tiny mobile text to large-scale environmental graphics.
                          </p>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Future-Proof Choices</h4>
                          <p className="text-sm text-green-700">
                            Invest in timeless typography that will serve your brand 
                            well for years to come, with room for evolution.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Related Resources */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Essential Brand Typography Resources</h2>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <a href="/font-psychology-guide" className="block p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                      <h3 className="font-semibold mb-2 text-purple-800">Font Psychology</h3>
                      <p className="text-sm text-purple-600">Understand the science behind font choices</p>
                    </a>
                    <a href="/typography-trends-2024" className="block p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                      <h3 className="font-semibold mb-2 text-blue-800">Typography Trends</h3>
                      <p className="text-sm text-blue-600">Stay current with design movements</p>
                    </a>
                    <a href="/font-accessibility-guide" className="block p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                      <h3 className="font-semibold mb-2 text-green-800">Accessibility Guide</h3>
                      <p className="text-sm text-green-600">Ensure inclusive brand typography</p>
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Build Your Brand Typography Strategy</h2>
              <p className="text-lg mb-6 opacity-90">
                Start experimenting with different font styles to find the perfect typography 
                that represents your brand and connects with your audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/" 
                  className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Building2 className="h-5 w-5 mr-2" />
                  Explore Brand Fonts
                </a>
                <a 
                  href="/font-psychology-guide" 
                  className="inline-flex items-center px-6 py-3 bg-blue-700 text-white font-semibold rounded-lg hover:bg-blue-800 transition-colors"
                >
                  <Zap className="h-5 w-5 mr-2" />
                  Learn Font Psychology
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default BrandFontSelectionGuide;
