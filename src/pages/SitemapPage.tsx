import { useEffect } from 'react'
import { generateSitemap } from '@/utils/sitemap'

const SitemapPage = () => {
  useEffect(() => {
    // Set document title for SEO
    document.title = 'Sitemap - Cursive Font Generator'
  }, [])

  const sitemapContent = generateSitemap()

  return (
    <div style={{ 
      fontFamily: 'monospace', 
      whiteSpace: 'pre-wrap', 
      padding: '20px',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: '4px'
    }}>
      <h1>Sitemap.xml</h1>
      <p>This is the auto-generated sitemap for cursivefontgenerator.top</p>
      <hr />
      {sitemapContent}
    </div>
  )
}

export default SitemapPage 