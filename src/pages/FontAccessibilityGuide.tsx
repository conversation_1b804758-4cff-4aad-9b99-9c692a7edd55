import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SE<PERSON>ead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, Heart, Users, Shield, CheckCircle, AlertTriangle, Lightbulb, Globe } from 'lucide-react';

const FontAccessibilityGuide: React.FC = () => {
  const accessibilityPrinciples = [
    {
      title: "Visual Clarity",
      icon: Eye,
      color: "blue",
      description: "Ensuring fonts are readable for users with visual impairments",
      guidelines: [
        "Use high contrast ratios (minimum 4.5:1 for normal text)",
        "Avoid decorative fonts for body text",
        "Ensure adequate font size (minimum 16px for web)",
        "Provide sufficient line spacing (1.5x font size minimum)"
      ],
      examples: {
        good: "𝖢𝗅𝖾𝖺𝗋 𝖺𝗇𝖽 𝖱𝖾𝖺𝖽𝖺𝖻𝗅𝖾",
        bad: "𝔇𝔦𝔣𝔣𝔦𝔠𝔲𝔩𝔱 𝔞𝔫𝔡 𝔇𝔢𝔠𝔬𝔯𝔞𝔱𝔦𝔳𝔢"
      }
    },
    {
      title: "Cognitive Accessibility",
      icon: Heart,
      color: "pink",
      description: "Supporting users with dyslexia and cognitive differences",
      guidelines: [
        "Choose fonts with distinct letter shapes",
        "Avoid fonts with mirrored characters (b/d, p/q)",
        "Use consistent font weights and styles",
        "Provide clear visual hierarchy"
      ],
      examples: {
        good: "𝖣𝗒𝗌𝗅𝖾𝗑𝗂𝖺-𝖿𝗋𝗂𝖾𝗇𝖽𝗅𝗒",
        bad: "𝒞𝑜𝓃𝒻𝓊𝓈𝒾𝓃𝑔 𝒮𝒸𝓇𝒾𝓅𝓉"
      }
    },
    {
      title: "Motor Accessibility",
      icon: Users,
      color: "green",
      description: "Accommodating users with motor impairments",
      guidelines: [
        "Ensure clickable text is large enough (44px minimum)",
        "Provide adequate spacing between interactive elements",
        "Use fonts that remain readable when zoomed to 200%",
        "Avoid fonts that require precise targeting"
      ],
      examples: {
        good: "𝗟𝗮𝗿𝗴𝗲 𝗮𝗻𝗱 𝗖𝗹𝗲𝗮𝗿",
        bad: "tiny script"
      }
    },
    {
      title: "Universal Design",
      icon: Globe,
      color: "purple",
      description: "Creating inclusive experiences for all users",
      guidelines: [
        "Test fonts with screen readers",
        "Ensure fonts work across different devices",
        "Consider cultural reading patterns",
        "Provide alternative text formats when needed"
      ],
      examples: {
        good: "𝖴𝗇𝗂𝗏𝖾𝗋𝗌𝖺𝗅 𝖣𝖾𝗌𝗂𝗀𝗇",
        bad: "𝔈𝔵𝔠𝔩𝔲𝔰𝔦𝔳𝔢 𝔞𝔫𝔡 ℌ𝔞𝔯𝔡"
      }
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "from-blue-50 to-indigo-50 border-blue-200",
      pink: "from-pink-50 to-rose-50 border-pink-200",
      green: "from-green-50 to-emerald-50 border-green-200",
      purple: "from-purple-50 to-violet-50 border-purple-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "text-blue-500",
      pink: "text-pink-500",
      green: "text-green-500",
      purple: "text-purple-500"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Font Accessibility Guide: Inclusive Typography for All Users"
        description="Learn how to create accessible typography that works for everyone. Comprehensive guide to inclusive font design, WCAG compliance, and universal accessibility."
        keywords="font accessibility, inclusive typography, WCAG fonts, dyslexia fonts, accessible design, universal design, inclusive fonts"
        canonical="https://cursivefontgenerator.com/font-accessibility-guide"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-emerald-900 via-teal-900 to-cyan-900 text-white">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Shield className="h-8 w-8 text-emerald-400 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold">
                  Font Accessibility Guide
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Create inclusive typography that works for everyone. Learn how to design with accessibility 
                in mind, ensuring your fonts are readable and usable by all users, regardless of their abilities.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Why Font Accessibility Matters</h2>
                  <div className="prose prose-lg max-w-none">
                    <p className="mb-4 leading-relaxed">
                      Font accessibility is about ensuring that typography is usable by people with diverse 
                      abilities and needs. This includes users with visual impairments, dyslexia, cognitive 
                      differences, motor impairments, and other disabilities. According to the World Health 
                      Organization, over 1 billion people worldwide live with some form of disability.
                    </p>
                    <p className="mb-4 leading-relaxed">
                      Accessible typography isn't just about compliance with guidelines like WCAG (Web Content 
                      Accessibility Guidelines)—it's about creating inclusive experiences that benefit everyone. 
                      Research shows that accessible design improves usability for all users, not just those 
                      with disabilities.
                    </p>
                  </div>
                  
                  <div className="grid md:grid-cols-3 gap-6 mt-8">
                    <div className="text-center">
                      <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="font-semibold mb-2">1+ Billion Users</h3>
                      <p className="text-sm text-gray-600">People worldwide with disabilities who benefit from accessible design</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <CheckCircle className="h-8 w-8 text-green-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Better for Everyone</h3>
                      <p className="text-sm text-gray-600">Accessible design improves usability for all users</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Shield className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Legal Compliance</h3>
                      <p className="text-sm text-gray-600">Meet WCAG guidelines and accessibility regulations</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Accessibility Principles */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto space-y-8">
              
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">Core Accessibility Principles</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Follow these fundamental principles to create typography that is accessible, 
                  inclusive, and usable by people with diverse abilities and needs.
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {accessibilityPrinciples.map((principle, index) => (
                  <Card key={index} className={`bg-gradient-to-br ${getColorClasses(principle.color)} border-2`}>
                    <CardContent className="p-8">
                      <div className="flex items-center mb-6">
                        <principle.icon className={`h-8 w-8 mr-4 ${getIconColor(principle.color)}`} />
                        <div>
                          <h3 className="text-xl font-bold">{principle.title}</h3>
                          <p className="text-gray-600">{principle.description}</p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-3">Guidelines:</h4>
                          <ul className="space-y-2">
                            {principle.guidelines.map((guideline, i) => (
                              <li key={i} className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                                <span className="text-sm text-gray-700">{guideline}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3">Examples:</h4>
                          <div className="bg-white p-4 rounded-lg space-y-3">
                            <div>
                              <span className="text-xs text-green-600 font-medium">✓ GOOD:</span>
                              <div className="text-lg mt-1">{principle.examples.good}</div>
                            </div>
                            <div>
                              <span className="text-xs text-red-600 font-medium">✗ AVOID:</span>
                              <div className="text-lg mt-1">{principle.examples.bad}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* WCAG Guidelines */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 flex items-center">
                    <Shield className="h-6 w-6 mr-3 text-blue-500" />
                    WCAG 2.1 Typography Guidelines
                  </h2>
                  
                  <div className="space-y-6">
                    <div className="border-l-4 border-green-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Level AA Requirements (Recommended)</h3>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold mb-3">Contrast Ratios:</h4>
                          <ul className="space-y-2 text-sm">
                            <li className="flex justify-between">
                              <span>Normal text:</span>
                              <span className="font-medium">4.5:1 minimum</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Large text (18pt+):</span>
                              <span className="font-medium">3:1 minimum</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Bold text (14pt+):</span>
                              <span className="font-medium">3:1 minimum</span>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-3">Size Requirements:</h4>
                          <ul className="space-y-2 text-sm">
                            <li className="flex justify-between">
                              <span>Minimum font size:</span>
                              <span className="font-medium">16px (web)</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Line height:</span>
                              <span className="font-medium">1.5x font size</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Paragraph spacing:</span>
                              <span className="font-medium">2x font size</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="border-l-4 border-blue-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Level AAA Requirements (Ideal)</h3>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold mb-3">Enhanced Contrast:</h4>
                          <ul className="space-y-2 text-sm">
                            <li className="flex justify-between">
                              <span>Normal text:</span>
                              <span className="font-medium">7:1 minimum</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Large text:</span>
                              <span className="font-medium">4.5:1 minimum</span>
                            </li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-3">Additional Requirements:</h4>
                          <ul className="space-y-2 text-sm">
                            <li>• No images of text (except logos)</li>
                            <li>• Text can be resized to 200% without loss</li>
                            <li>• No horizontal scrolling at 320px width</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 flex items-center">
                    <Lightbulb className="h-6 w-6 mr-3 text-yellow-500" />
                    Practical Implementation Tips
                  </h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-green-600">✅ Best Practices</h3>
                      <div className="space-y-4">
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Choose Accessible Fonts</h4>
                          <p className="text-sm text-green-700">
                            Select fonts specifically designed for readability, such as those with 
                            distinct character shapes and good spacing.
                          </p>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Test with Real Users</h4>
                          <p className="text-sm text-green-700">
                            Include users with disabilities in your testing process to get 
                            authentic feedback on font accessibility.
                          </p>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Use Accessibility Tools</h4>
                          <p className="text-sm text-green-700">
                            Leverage screen readers, contrast checkers, and other accessibility 
                            tools during the design process.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-red-600">❌ Common Mistakes</h3>
                      <div className="space-y-4">
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Relying Only on Color</h4>
                          <p className="text-sm text-red-700">
                            Don't use color alone to convey information. Always provide 
                            additional visual cues like font weight or icons.
                          </p>
                        </div>
                        
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Ignoring Mobile Users</h4>
                          <p className="text-sm text-red-700">
                            Ensure fonts remain accessible on small screens and touch 
                            interfaces, not just desktop computers.
                          </p>
                        </div>
                        
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Assuming One Size Fits All</h4>
                          <p className="text-sm text-red-700">
                            Different users have different needs. Provide options for 
                            font size adjustment when possible.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Testing Your Font Accessibility</h2>
                  
                  <div className="space-y-6">
                    <div className="bg-blue-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Automated Testing Tools</h3>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold mb-2">Contrast Checkers:</h4>
                          <ul className="text-sm space-y-1">
                            <li>• WebAIM Contrast Checker</li>
                            <li>• Colour Contrast Analyser</li>
                            <li>• WAVE Web Accessibility Evaluator</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Screen Readers:</h4>
                          <ul className="text-sm space-y-1">
                            <li>• NVDA (Windows, free)</li>
                            <li>• JAWS (Windows, commercial)</li>
                            <li>• VoiceOver (macOS/iOS, built-in)</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Manual Testing Checklist</h3>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold mb-2">Visual Tests:</h4>
                          <ul className="text-sm space-y-1">
                            <li>□ Zoom to 200% - still readable?</li>
                            <li>□ Test in bright sunlight</li>
                            <li>□ Check with color blindness simulators</li>
                            <li>□ Verify on different screen sizes</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Functional Tests:</h4>
                          <ul className="text-sm space-y-1">
                            <li>□ Navigate using only keyboard</li>
                            <li>□ Test with screen reader</li>
                            <li>□ Check focus indicators</li>
                            <li>□ Verify touch target sizes</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16 bg-gradient-to-r from-emerald-600 to-teal-600 text-white">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Create Accessible Typography Today</h2>
              <p className="text-lg mb-6 opacity-90">
                Start implementing accessible font practices in your projects. Every step toward 
                inclusive design makes the web better for everyone.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/" 
                  className="inline-flex items-center px-6 py-3 bg-white text-emerald-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Shield className="h-5 w-5 mr-2" />
                  Try Accessible Fonts
                </a>
                <a 
                  href="/font-psychology-guide" 
                  className="inline-flex items-center px-6 py-3 bg-emerald-700 text-white font-semibold rounded-lg hover:bg-emerald-800 transition-colors"
                >
                  <Lightbulb className="h-5 w-5 mr-2" />
                  Learn More
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FontAccessibilityGuide;
