import { useNavigate } from 'react-router-dom'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Home, Search, ArrowLeft, Sparkles } from 'lucide-react'
import SEOHead from '@/components/SEOHead'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

function NotFoundPage() {
  const navigate = useNavigate()

  return (
    <>
      <SEOHead
        title="Page Not Found - Cursive Font Generator"
        description="The page you're looking for doesn't exist. Return to Cursive Font Generator homepage to explore our font styling tools and generators."
        canonical="https://cursivefontgenerator.top/404"
        keywords="404 error, page not found, cursive font generator"
      />
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center px-4 py-16">
          <div className="max-w-2xl mx-auto text-center space-y-8">
            {/* 404 Display */}
            <div className="space-y-4">
              <div className="text-8xl font-bold text-primary/20 select-none">
                404
              </div>
              <h1 className="text-4xl font-bold">Page Not Found</h1>
              <p className="text-xl text-muted-foreground">
                Oops! The page you're looking for doesn't exist or has been moved.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                onClick={() => navigate('/')}
                className="h-12 px-6 text-base"
                size="lg"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Homepage
              </Button>
              <Button
                onClick={() => navigate(-1)}
                variant="outline"
                className="h-12 px-6 text-base"
                size="lg"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>

            {/* Popular Pages */}
            <Card className="text-left">
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <Sparkles className="mr-2 h-5 w-5 text-primary" />
                  Popular Pages
                </h2>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <a
                      href="/"
                      className="block p-3 rounded-lg hover:bg-muted transition-colors"
                    >
                      <h3 className="font-medium">Font Generator</h3>
                      <p className="text-sm text-muted-foreground">
                        Transform text into stylish fonts
                      </p>
                    </a>
                    <a
                      href="/cursive-fonts"
                      className="block p-3 rounded-lg hover:bg-muted transition-colors"
                    >
                      <h3 className="font-medium">Cursive Fonts</h3>
                      <p className="text-sm text-muted-foreground">
                        Elegant script and handwriting styles
                      </p>
                    </a>
                  </div>
                  <div className="space-y-3">
                    <a
                      href="/tattoo-fonts"
                      className="block p-3 rounded-lg hover:bg-muted transition-colors"
                    >
                      <h3 className="font-medium">Tattoo Fonts</h3>
                      <p className="text-sm text-muted-foreground">
                        Gothic and Old English fonts
                      </p>
                    </a>
                    <a
                      href="/about"
                      className="block p-3 rounded-lg hover:bg-muted transition-colors"
                    >
                      <h3 className="font-medium">About Us</h3>
                      <p className="text-sm text-muted-foreground">
                        Learn about our mission and team
                      </p>
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Help Text */}
            <div className="text-center space-y-2">
              <p className="text-muted-foreground">
                If you believe this is an error, please <a href="/contact" className="text-primary hover:underline">contact us</a>.
              </p>
              <p className="text-sm text-muted-foreground">
                Error Code: 404 | Page Not Found
              </p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  )
}

export default NotFoundPage 