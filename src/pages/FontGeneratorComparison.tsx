import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Check, X, Star, Zap, Shield, Globe, Smartphone, Download } from 'lucide-react';

const FontGeneratorComparison: React.FC = () => {
  const comparisonData = [
    {
      name: "Cursive Font Generator",
      isOurs: true,
      logo: "🎨",
      rating: 5,
      features: {
        "Unicode Support": true,
        "No Download Required": true,
        "Mobile Friendly": true,
        "Free to Use": true,
        "Commercial License": true,
        "Multiple Font Styles": true,
        "Copy & Paste": true,
        "Cross-Platform": true,
        "Regular Updates": true,
        "Customer Support": true,
        "Ad-Free Experience": true,
        "Offline Access": false
      },
      pros: [
        "Extensive Unicode font collection",
        "Works on all devices and platforms",
        "No registration or download required",
        "Professional quality fonts",
        "Fast and responsive interface"
      ],
      cons: [
        "Requires internet connection",
        "Limited to Unicode character sets"
      ],
      bestFor: "Social media, web content, quick font generation"
    },
    {
      name: "Traditional Font Software",
      isOurs: false,
      logo: "💻",
      rating: 3,
      features: {
        "Unicode Support": false,
        "No Download Required": false,
        "Mobile Friendly": false,
        "Free to Use": false,
        "Commercial License": false,
        "Multiple Font Styles": true,
        "Copy & Paste": false,
        "Cross-Platform": false,
        "Regular Updates": false,
        "Customer Support": true,
        "Ad-Free Experience": true,
        "Offline Access": true
      },
      pros: [
        "Professional design tools",
        "Offline functionality",
        "Advanced customization options"
      ],
      cons: [
        "Expensive licensing fees",
        "Requires installation and setup",
        "Not mobile-friendly",
        "Steep learning curve",
        "Platform-specific limitations"
      ],
      bestFor: "Professional graphic design, print media"
    },
    {
      name: "Basic Online Generators",
      isOurs: false,
      logo: "🌐",
      rating: 2,
      features: {
        "Unicode Support": false,
        "No Download Required": true,
        "Mobile Friendly": false,
        "Free to Use": true,
        "Commercial License": false,
        "Multiple Font Styles": false,
        "Copy & Paste": true,
        "Cross-Platform": false,
        "Regular Updates": false,
        "Customer Support": false,
        "Ad-Free Experience": false,
        "Offline Access": false
      },
      pros: [
        "Free to use",
        "No download required",
        "Simple interface"
      ],
      cons: [
        "Limited font options",
        "Poor mobile experience",
        "Unreliable service",
        "Ad-heavy interfaces",
        "No customer support"
      ],
      bestFor: "Basic text styling, casual use"
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Font Generator Comparison 2024: Best Unicode Font Tools Reviewed"
        description="Compare the best font generators available. See detailed feature comparisons, pros and cons, and find the perfect font tool for your needs."
        keywords="font generator comparison, best font tools, unicode font generator, font software review, online font generator"
        canonical="https://cursivefontgenerator.com/font-generator-comparison"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-cyan-50 via-blue-50 to-indigo-50">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Zap className="h-8 w-8 text-cyan-500 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold gradient-text">
                  Font Generator Comparison
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Compare the best font generators available today. We've analyzed features, usability, 
                and value to help you choose the perfect font tool for your creative projects.
              </p>
            </div>
          </div>
        </section>

        {/* Comparison Table */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-2xl font-bold text-center mb-8">Feature Comparison</h2>
              
              <div className="grid lg:grid-cols-3 gap-6">
                {comparisonData.map((tool, index) => (
                  <Card key={index} className={`relative ${tool.isOurs ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}>
                    {tool.isOurs && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                          Recommended
                        </span>
                      </div>
                    )}
                    
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        <div className="text-4xl mb-2">{tool.logo}</div>
                        <h3 className="text-xl font-bold mb-2">{tool.name}</h3>
                        <div className="flex items-center justify-center mb-2">
                          {renderStars(tool.rating)}
                        </div>
                        <p className="text-sm text-gray-600">{tool.bestFor}</p>
                      </div>

                      <div className="space-y-3 mb-6">
                        {Object.entries(tool.features).map(([feature, supported]) => (
                          <div key={feature} className="flex items-center justify-between">
                            <span className="text-sm">{feature}</span>
                            {supported ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        ))}
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-green-600 mb-2">✅ Pros</h4>
                          <ul className="text-sm space-y-1">
                            {tool.pros.map((pro, i) => (
                              <li key={i} className="text-gray-700">• {pro}</li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold text-red-600 mb-2">❌ Cons</h4>
                          <ul className="text-sm space-y-1">
                            {tool.cons.map((con, i) => (
                              <li key={i} className="text-gray-700">• {con}</li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {tool.isOurs && (
                        <div className="mt-6 pt-4 border-t">
                          <a 
                            href="/"
                            className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors text-center block font-semibold"
                          >
                            Try Our Generator
                          </a>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Detailed Analysis */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Why Choose Unicode Font Generators?</h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4 flex items-center">
                        <Globe className="h-5 w-5 mr-2 text-blue-500" />
                        Universal Compatibility
                      </h3>
                      <p className="text-gray-700 mb-4">
                        Unicode fonts work across all platforms, devices, and applications without requiring 
                        special software or font installations. This makes them perfect for modern digital 
                        communication where content needs to be accessible everywhere.
                      </p>
                      <ul className="list-disc pl-6 space-y-1 text-sm text-gray-600">
                        <li>Works on Windows, Mac, iOS, Android</li>
                        <li>Compatible with all major browsers</li>
                        <li>Displays correctly in emails and messages</li>
                        <li>No font licensing issues</li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold mb-4 flex items-center">
                        <Smartphone className="h-5 w-5 mr-2 text-green-500" />
                        Mobile-First Design
                      </h3>
                      <p className="text-gray-700 mb-4">
                        With over 60% of web traffic coming from mobile devices, having a font generator 
                        that works seamlessly on smartphones and tablets is crucial. Our tool is optimized 
                        for touch interfaces and small screens.
                      </p>
                      <ul className="list-disc pl-6 space-y-1 text-sm text-gray-600">
                        <li>Touch-friendly interface</li>
                        <li>Responsive design</li>
                        <li>Fast loading on mobile networks</li>
                        <li>Easy copy and paste on mobile</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Common Font Generator Limitations</h2>
                  
                  <div className="space-y-6">
                    <div className="bg-red-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-3 text-red-800">Platform Dependency</h3>
                      <p className="text-red-700 mb-3">
                        Many traditional font tools only work on specific operating systems or require 
                        expensive software licenses.
                      </p>
                      <p className="text-sm text-red-600">
                        <strong>Our Solution:</strong> Web-based tool that works on any device with an internet connection.
                      </p>
                    </div>

                    <div className="bg-yellow-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-3 text-yellow-800">Limited Font Selection</h3>
                      <p className="text-yellow-700 mb-3">
                        Basic online generators often have very few font options and poor quality output.
                      </p>
                      <p className="text-sm text-yellow-600">
                        <strong>Our Solution:</strong> Extensive collection of high-quality Unicode fonts across multiple categories.
                      </p>
                    </div>

                    <div className="bg-blue-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-3 text-blue-800">Poor User Experience</h3>
                      <p className="text-blue-700 mb-3">
                        Many font generators have cluttered interfaces, slow loading times, and intrusive advertisements.
                      </p>
                      <p className="text-sm text-blue-600">
                        <strong>Our Solution:</strong> Clean, fast, ad-free interface designed for optimal user experience.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Choosing the Right Font Generator</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-3">For Social Media Creators</h3>
                      <p className="text-gray-700 mb-3">
                        If you're creating content for Instagram, Facebook, Twitter, or other social platforms, 
                        you need a generator that produces Unicode fonts that work across all platforms.
                      </p>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-700">
                          <strong>Recommended:</strong> Our Unicode font generator with extensive cursive, bold, 
                          and decorative options specifically tested for social media compatibility.
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3">For Professional Design</h3>
                      <p className="text-gray-700 mb-3">
                        Professional designers working on print materials or complex layouts may need 
                        traditional font software with advanced features.
                      </p>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-700">
                          <strong>Consider:</strong> Professional design software for complex projects, 
                          but use our generator for quick digital content and social media.
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3">For Casual Users</h3>
                      <p className="text-gray-700 mb-3">
                        If you just need to create stylized text occasionally for personal use, 
                        simplicity and ease of use are most important.
                      </p>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-700">
                          <strong>Perfect Choice:</strong> Our user-friendly interface requires no learning curve 
                          and produces professional results instantly.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card className="bg-gradient-to-r from-cyan-600 to-blue-600 text-white">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-bold mb-4">Experience the Difference</h2>
                  <p className="text-lg mb-6 opacity-90">
                    Try our font generator and see why thousands of creators choose us for their typography needs.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a 
                      href="/" 
                      className="inline-flex items-center px-6 py-3 bg-white text-cyan-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <Zap className="h-5 w-5 mr-2" />
                      Try Free Now
                    </a>
                    <a 
                      href="/font-inspiration-showcase" 
                      className="inline-flex items-center px-6 py-3 bg-cyan-700 text-white font-semibold rounded-lg hover:bg-cyan-800 transition-colors"
                    >
                      <Star className="h-5 w-5 mr-2" />
                      See Examples
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FontGeneratorComparison;
