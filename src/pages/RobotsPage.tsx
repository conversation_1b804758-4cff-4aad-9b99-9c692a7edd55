import { useEffect } from 'react'
import { generateRobotsTxt } from '@/utils/sitemap'

const RobotsPage = () => {
  useEffect(() => {
    // Set document title for SEO
    document.title = 'Robots.txt - Cursive Font Generator'
  }, [])

  const robotsContent = generateRobotsTxt()

  return (
    <div style={{ 
      fontFamily: 'monospace', 
      whiteSpace: 'pre-wrap', 
      padding: '20px',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: '4px'
    }}>
      <h1>Robots.txt</h1>
      <p>This is the robots.txt file for cursivefontgenerator.top</p>
      <hr />
      {robotsContent}
    </div>
  )
}

export default RobotsPage 