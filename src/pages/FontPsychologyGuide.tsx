import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Brain, Heart, Eye, Zap, Target, Users, TrendingUp, Lightbulb } from 'lucide-react';

const FontPsychologyGuide: React.FC = () => {
  const psychologyPrinciples = [
    {
      title: "Cognitive Load Theory",
      icon: Brain,
      color: "blue",
      description: "How font complexity affects mental processing",
      examples: [
        { font: "𝒮𝒾𝓂𝓅𝓁𝑒", load: "Low", effect: "Easy to process, reduces mental fatigue" },
        { font: "𝔠𝔬𝔪𝔭𝔩𝔢𝔵", load: "High", effect: "Requires more attention, can overwhelm" }
      ],
      applications: ["User interfaces", "Educational content", "Long-form reading"]
    },
    {
      title: "Emotional Connotation",
      icon: Heart,
      color: "pink",
      description: "How fonts trigger emotional responses",
      examples: [
        { font: "𝓔𝓵𝓮𝓰𝓪𝓷𝓽", emotion: "Sophistication", effect: "Creates premium perception" },
        { font: "𝗕𝗼𝗹𝗱", emotion: "Confidence", effect: "Conveys strength and authority" }
      ],
      applications: ["Brand identity", "Marketing materials", "Emotional messaging"]
    },
    {
      title: "Readability Psychology",
      icon: Eye,
      color: "green",
      description: "How font design affects reading comprehension",
      examples: [
        { font: "Clear Sans", readability: "High", effect: "Improves comprehension speed" },
        { font: "𝔇𝔢𝔠𝔬𝔯𝔞𝔱𝔦𝔳𝔢", readability: "Low", effect: "Slows reading, increases errors" }
      ],
      applications: ["Educational materials", "Technical documentation", "Accessibility design"]
    },
    {
      title: "Attention Mechanisms",
      icon: Zap,
      color: "yellow",
      description: "How fonts capture and direct attention",
      examples: [
        { font: "𝗨𝗥𝗚𝗘𝗡𝗧", attention: "High", effect: "Immediately draws focus" },
        { font: "subtle", attention: "Low", effect: "Blends into background" }
      ],
      applications: ["Call-to-action buttons", "Warning messages", "Headlines"]
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "from-blue-50 to-indigo-50 border-blue-200",
      pink: "from-pink-50 to-rose-50 border-pink-200",
      green: "from-green-50 to-emerald-50 border-green-200",
      yellow: "from-yellow-50 to-orange-50 border-yellow-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "text-blue-500",
      pink: "text-pink-500",
      green: "text-green-500",
      yellow: "text-yellow-500"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Font Psychology Guide: How Typography Affects Human Behavior & Emotions"
        description="Discover the psychological impact of fonts on human behavior. Learn how typography influences emotions, cognition, and decision-making in design."
        keywords="font psychology, typography psychology, font emotions, cognitive typography, font behavior, design psychology"
        canonical="https://cursivefontgenerator.com/font-psychology-guide"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Brain className="h-8 w-8 text-purple-400 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold">
                  The Psychology of Fonts
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Explore the fascinating science behind how fonts influence human behavior, emotions, and 
                decision-making. Understand the psychological principles that make typography powerful.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">The Science Behind Font Psychology</h2>
                  <div className="prose prose-lg max-w-none">
                    <p className="mb-4 leading-relaxed">
                      Typography psychology is a fascinating field that examines how different fonts and typefaces 
                      influence human perception, emotion, and behavior. Research in cognitive science and 
                      neuroscience has revealed that fonts are not merely aesthetic choices—they are powerful 
                      psychological tools that can significantly impact how people process information, make 
                      decisions, and form impressions.
                    </p>
                    <p className="mb-4 leading-relaxed">
                      When we encounter text, our brains process not just the content but also the visual 
                      characteristics of the letters themselves. This processing happens in milliseconds, 
                      often below the threshold of conscious awareness, yet it profoundly influences our 
                      understanding and emotional response to the message.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Psychology Principles */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto space-y-8">
              
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">Core Psychological Principles</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Understanding these fundamental principles will help you choose fonts that align with 
                  your communication goals and audience psychology.
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {psychologyPrinciples.map((principle, index) => (
                  <Card key={index} className={`bg-gradient-to-br ${getColorClasses(principle.color)} border-2`}>
                    <CardContent className="p-8">
                      <div className="flex items-center mb-6">
                        <principle.icon className={`h-8 w-8 mr-4 ${getIconColor(principle.color)}`} />
                        <div>
                          <h3 className="text-xl font-bold">{principle.title}</h3>
                          <p className="text-gray-600">{principle.description}</p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-3">Examples & Effects:</h4>
                          <div className="space-y-3">
                            {principle.examples.map((example, i) => (
                              <div key={i} className="bg-white p-4 rounded-lg">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-lg font-medium">{example.font}</span>
                                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                                    'load' in example ? 
                                      (example.load === 'Low' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') :
                                    'emotion' in example ?
                                      'bg-purple-100 text-purple-800' :
                                    'readability' in example ?
                                      (example.readability === 'High' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') :
                                      'bg-blue-100 text-blue-800'
                                  }`}>
                                    {'load' in example ? example.load : 
                                     'emotion' in example ? example.emotion :
                                     'readability' in example ? example.readability :
                                     'attention' in example ? example.attention : ''}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-700">{example.effect}</p>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-2">Best Applications:</h4>
                          <div className="flex flex-wrap gap-2">
                            {principle.applications.map((app, i) => (
                              <span key={i} className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
                                {app}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Emotional Impact */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 flex items-center">
                    <Heart className="h-6 w-6 mr-3 text-red-500" />
                    Emotional Associations of Font Styles
                  </h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">Positive Emotions</h3>
                      <div className="space-y-4">
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Trust & Reliability</h4>
                          <p className="text-sm text-gray-700 mb-2">Clean, simple fonts convey honesty and dependability.</p>
                          <div className="text-lg">𝖢𝗅𝖾𝖺𝗇 & 𝖳𝗋𝗎𝗌𝗍𝗐𝗈𝗋𝗍𝗁𝗒</div>
                        </div>
                        
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Elegance & Sophistication</h4>
                          <p className="text-sm text-gray-700 mb-2">Script fonts evoke luxury and refinement.</p>
                          <div className="text-lg">𝒮𝑜𝓅𝒽𝒾𝓈𝓉𝒾𝒸𝒶𝓉𝑒𝒹 & 𝐸𝓁𝑒𝑔𝒶𝓃𝓉</div>
                        </div>
                        
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Energy & Excitement</h4>
                          <p className="text-sm text-gray-700 mb-2">Bold fonts create enthusiasm and urgency.</p>
                          <div className="text-lg">𝗘𝗻𝗲𝗿𝗴𝗲𝘁𝗶𝗰 & 𝗘𝘅𝗰𝗶𝘁𝗶𝗻𝗴</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold mb-4">Contextual Emotions</h3>
                      <div className="space-y-4">
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Creativity & Innovation</h4>
                          <p className="text-sm text-gray-700 mb-2">Unique fonts suggest originality and artistic vision.</p>
                          <div className="text-lg">𝒞𝓇𝑒𝒶𝓉𝒾𝓋𝑒 & 𝐼𝓃𝓃𝑜𝓋𝒶𝓉𝒾𝓋𝑒</div>
                        </div>
                        
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Tradition & Heritage</h4>
                          <p className="text-sm text-gray-700 mb-2">Classic fonts evoke history and established values.</p>
                          <div className="text-lg">𝔗𝔯𝔞𝔡𝔦𝔱𝔦𝔬𝔫𝔞𝔩 & ℌ𝔦𝔰𝔱𝔬𝔯𝔦𝔠</div>
                        </div>
                        
                        <div className="bg-orange-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Playfulness & Fun</h4>
                          <p className="text-sm text-gray-700 mb-2">Casual fonts create approachable, friendly feelings.</p>
                          <div className="text-lg">𝓅𝓁𝒶𝓎𝒻𝓊𝓁 & 𝒻𝓊𝓃</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 flex items-center">
                    <Target className="h-6 w-6 mr-3 text-blue-500" />
                    Practical Applications in Design
                  </h2>
                  
                  <div className="space-y-6">
                    <div className="border-l-4 border-blue-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Brand Identity Design</h3>
                      <p className="text-gray-700 mb-3">
                        Font choice is crucial for brand personality. A tech startup might use clean, modern fonts 
                        to convey innovation, while a luxury brand might choose elegant scripts to suggest exclusivity.
                      </p>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-700">
                          <strong>Research Finding:</strong> Consumers form brand impressions within 50 milliseconds, 
                          with typography playing a major role in this rapid assessment.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">User Interface Design</h3>
                      <p className="text-gray-700 mb-3">
                        In digital interfaces, font psychology affects usability and user satisfaction. Clear, 
                        readable fonts reduce cognitive load and improve task completion rates.
                      </p>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-700">
                          <strong>UX Insight:</strong> Users complete tasks 12% faster when interfaces use 
                          psychologically appropriate fonts for their context.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-purple-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Marketing & Advertising</h3>
                      <p className="text-gray-700 mb-3">
                        Marketing materials leverage font psychology to influence purchasing decisions. 
                        Bold fonts create urgency, while elegant fonts suggest premium quality.
                      </p>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-700">
                          <strong>Marketing Data:</strong> A/B tests show that psychologically aligned fonts 
                          can increase conversion rates by up to 38%.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Related Resources */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Explore More Psychology Topics</h2>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <a href="/brand-font-selection-guide" className="block p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                      <h3 className="font-semibold mb-2 text-blue-800">Brand Font Selection</h3>
                      <p className="text-sm text-blue-600">Apply psychology to brand strategy</p>
                    </a>
                    <a href="/social-media-font-tips" className="block p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                      <h3 className="font-semibold mb-2 text-green-800">Social Media Psychology</h3>
                      <p className="text-sm text-green-600">Platform-specific psychological insights</p>
                    </a>
                    <a href="/font-accessibility-guide" className="block p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                      <h3 className="font-semibold mb-2 text-purple-800">Accessibility Psychology</h3>
                      <p className="text-sm text-purple-600">Inclusive design principles</p>
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Apply Font Psychology to Your Projects</h2>
              <p className="text-lg mb-6 opacity-90">
                Use our font generators to experiment with different psychological effects and find the perfect 
                typography for your specific goals and audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/" 
                  className="inline-flex items-center px-6 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Brain className="h-5 w-5 mr-2" />
                  Explore Font Psychology
                </a>
                <a 
                  href="/font-inspiration-showcase" 
                  className="inline-flex items-center px-6 py-3 bg-purple-700 text-white font-semibold rounded-lg hover:bg-purple-800 transition-colors"
                >
                  <Lightbulb className="h-5 w-5 mr-2" />
                  See Examples
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FontPsychologyGuide;
