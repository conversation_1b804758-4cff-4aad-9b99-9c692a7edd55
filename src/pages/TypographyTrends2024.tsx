import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, Calendar, Eye, Smartphone, Globe, Zap, Target, BarChart3 } from 'lucide-react';

const TypographyTrends2024: React.FC = () => {
  const trends = [
    {
      title: "Retro Revival",
      icon: Calendar,
      color: "orange",
      description: "80s and 90s aesthetics dominating digital design",
      examples: ["ＶＡＰＯＲＷＡＶＥ", "𝔯𝔢𝔱𝔯𝔬 𝔤𝞞𝔪𝔦𝔫𝔤", "ｎｏｓｔａｌｇｉａ"],
      impact: "High",
      platforms: ["Gaming", "Entertainment", "Fashion"],
      prediction: "Will continue growing through 2025"
    },
    {
      title: "Minimalist Bold",
      icon: Eye,
      color: "blue",
      description: "Clean, bold typography for maximum impact",
      examples: ["𝗠𝗜𝗡𝗜𝗠𝗔𝗟", "𝖢𝖫𝖤𝖠𝖭", "𝐁𝐎𝐋𝐃"],
      impact: "Very High",
      platforms: ["Tech", "Startups", "Corporate"],
      prediction: "Becoming the new standard"
    },
    {
      title: "Handwritten Authenticity",
      icon: Target,
      color: "green",
      description: "Personal touch in digital communications",
      examples: ["𝒽𝒶𝓃𝒹𝓌𝓇𝒾𝓉𝓉𝑒𝓃", "𝓅𝑒𝓇𝓈𝑜𝓃𝒶𝓁", "𝒶𝓊𝓉𝒽𝑒𝓃𝓉𝒾𝒸"],
      impact: "Medium",
      platforms: ["Social Media", "Personal Brands", "Lifestyle"],
      prediction: "Steady growth in personal branding"
    },
    {
      title: "Variable Font Innovation",
      icon: Zap,
      color: "purple",
      description: "Dynamic fonts that adapt to context",
      examples: ["𝕕𝕪𝕟𝕒𝕞𝕚𝕔", "𝒶𝒹𝒶𝓅𝓉𝒾𝓋𝑒", "𝚏𝚕𝚞𝚒𝚍"],
      impact: "Emerging",
      platforms: ["Web Design", "Apps", "Interactive Media"],
      prediction: "Major breakthrough expected in 2025"
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      orange: "from-orange-50 to-red-50 border-orange-200",
      blue: "from-blue-50 to-indigo-50 border-blue-200",
      green: "from-green-50 to-emerald-50 border-green-200",
      purple: "from-purple-50 to-violet-50 border-purple-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      orange: "text-orange-500",
      blue: "text-blue-500",
      green: "text-green-500",
      purple: "text-purple-500"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Typography Trends 2024: Latest Font Design Insights & Predictions"
        description="Discover the latest typography trends shaping 2024. From retro revival to minimalist bold, explore current font design movements and future predictions."
        keywords="typography trends 2024, font design trends, typography predictions, modern fonts, design trends, font styles 2024"
        canonical="https://cursivefontgenerator.com/typography-trends-2024"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <TrendingUp className="h-8 w-8 text-pink-400 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold">
                  Typography Trends 2024
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Explore the cutting-edge typography trends defining 2024. From retro revivals to 
                minimalist innovations, discover what's shaping the future of digital design.
              </p>
            </div>
          </div>
        </section>

        {/* Trend Analysis */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto space-y-8">
              
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">Major Typography Movements</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Based on analysis of design platforms, social media trends, and industry reports, 
                  these are the typography movements defining 2024.
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {trends.map((trend, index) => (
                  <Card key={index} className={`bg-gradient-to-br ${getColorClasses(trend.color)} border-2`}>
                    <CardContent className="p-8">
                      <div className="flex items-center mb-6">
                        <trend.icon className={`h-8 w-8 mr-4 ${getIconColor(trend.color)}`} />
                        <div>
                          <h3 className="text-xl font-bold">{trend.title}</h3>
                          <p className="text-gray-600">{trend.description}</p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold mb-2">Examples:</h4>
                          <div className="bg-white p-4 rounded-lg">
                            {trend.examples.map((example, i) => (
                              <div key={i} className="text-lg mb-1">{example}</div>
                            ))}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold mb-2">Impact Level:</h4>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                              trend.impact === 'Very High' ? 'bg-red-100 text-red-800' :
                              trend.impact === 'High' ? 'bg-orange-100 text-orange-800' :
                              trend.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {trend.impact}
                            </span>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">Key Platforms:</h4>
                            <div className="flex flex-wrap gap-1">
                              {trend.platforms.map((platform, i) => (
                                <span key={i} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                  {platform}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-2">2025 Prediction:</h4>
                          <p className="text-sm text-gray-700">{trend.prediction}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Platform Analysis */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 text-center flex items-center justify-center">
                    <BarChart3 className="h-6 w-6 mr-3 text-blue-500" />
                    Platform-Specific Trends
                  </h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4 flex items-center">
                        <Smartphone className="h-5 w-5 mr-2 text-green-500" />
                        Social Media Platforms
                      </h3>
                      <div className="space-y-4">
                        <div className="bg-pink-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Instagram</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Aesthetic <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">cursive fonts</a> and minimalist bold text dominate stories and posts.
                            Try our <a href="/elegant-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">elegant cursive fonts</a> for Instagram.
                          </p>
                          <div className="text-lg">𝒾𝓃𝓈𝓉𝒶 𝒶𝑒𝓈𝓉𝒽𝑒𝓉𝒾𝒸</div>
                        </div>
                        
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">TikTok</h4>
                          <p className="text-sm text-gray-700 mb-2">Bold, attention-grabbing fonts that work well in short-form video content.</p>
                          <div className="text-lg">𝗧𝗶𝗸𝗧𝗼𝗸 𝗧𝗿𝗲𝗻𝗱𝘀</div>
                        </div>
                        
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Twitter/X</h4>
                          <p className="text-sm text-gray-700 mb-2">Clean, readable fonts that maintain impact in limited character counts.</p>
                          <div className="text-lg">𝚌𝚕𝚎𝚊𝚗 & 𝚛𝚎𝚊𝚍𝚊𝚋𝚕𝚎</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold mb-4 flex items-center">
                        <Globe className="h-5 w-5 mr-2 text-blue-500" />
                        Industry Applications
                      </h3>
                      <div className="space-y-4">
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Tech & Startups</h4>
                          <p className="text-sm text-gray-700 mb-2">Minimalist sans-serif fonts that convey innovation and reliability.</p>
                          <div className="text-lg">𝗶𝗻𝗻𝗼𝘃𝗮𝘁𝗶𝗼𝗻</div>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Fashion & Lifestyle</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            <a href="/elegant-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">Elegant cursive fonts</a> and sophisticated serif fonts for luxury appeal.
                            Explore our <a href="/romantic-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">romantic cursive collection</a>.
                          </p>
                          <div className="text-lg">𝓮𝓵𝓮𝓰𝓪𝓷𝓬𝓮</div>
                        </div>
                        
                        <div className="bg-orange-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Gaming & Entertainment</h4>
                          <p className="text-sm text-gray-700 mb-2">Retro and futuristic fonts that create immersive experiences.</p>
                          <div className="text-lg">ＧＡＭＩＮＧ</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Future Predictions */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">2025 Typography Predictions</h2>
                  
                  <div className="space-y-6">
                    <div className="border-l-4 border-blue-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">AI-Generated Custom Fonts</h3>
                      <p className="text-gray-700 mb-3">
                        Artificial intelligence will enable real-time font generation tailored to specific content, 
                        brand voice, and audience preferences.
                      </p>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-700">
                          <strong>Impact:</strong> Democratization of custom typography, making unique fonts accessible to all creators.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Accessibility-First Design</h3>
                      <p className="text-gray-700 mb-3">
                        Typography will prioritize readability and accessibility, with fonts designed specifically 
                        for users with dyslexia, visual impairments, and other accessibility needs.
                      </p>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-700">
                          <strong>Impact:</strong> Universal design principles becoming standard in font development.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-purple-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Interactive Typography</h3>
                      <p className="text-gray-700 mb-3">
                        Fonts will become interactive elements that respond to user behavior, device orientation, 
                        time of day, and environmental factors.
                      </p>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-700">
                          <strong>Impact:</strong> Typography as a dynamic interface element rather than static text.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-orange-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Sustainable Typography</h3>
                      <p className="text-gray-700 mb-3">
                        Environmental consciousness will drive the development of fonts optimized for energy 
                        efficiency, reducing screen power consumption and digital carbon footprints.
                      </p>
                      <div className="bg-orange-50 p-4 rounded-lg">
                        <p className="text-sm text-orange-700">
                          <strong>Impact:</strong> Eco-friendly design considerations becoming part of font selection criteria.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">How to Stay Ahead of Typography Trends</h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">For Designers</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          <span className="text-sm">Follow design platforms like Behance, Dribbble, and Awwwards</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          <span className="text-sm">Experiment with variable fonts and new technologies</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          <span className="text-sm">Study typography in different cultural contexts</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          <span className="text-sm">Attend typography conferences and workshops</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-4">For Content Creators</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span className="text-sm">Test different font styles with your audience</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span className="text-sm">Monitor engagement metrics for typography choices</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span className="text-sm">Stay updated on platform-specific font trends</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span className="text-sm">Balance trendy fonts with brand consistency</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Stay Ahead of Typography Trends</h2>
              <p className="text-lg mb-6 opacity-90">
                Experiment with the latest font styles and discover what works best for your brand and audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/cursive-fonts"
                  className="inline-flex items-center px-6 py-3 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Try Trending Cursive Fonts
                </a>
                <a
                  href="/elegant-cursive-fonts"
                  className="inline-flex items-center px-6 py-3 bg-indigo-700 text-white font-semibold rounded-lg hover:bg-indigo-800 transition-colors"
                >
                  <Eye className="h-5 w-5 mr-2" />
                  Explore Elegant Cursive
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default TypographyTrends2024;
