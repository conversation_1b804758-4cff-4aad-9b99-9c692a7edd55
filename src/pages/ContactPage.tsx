import { Mail, MessageCircle, Clock, Users, Heart } from "lucide-react"
import PageLayout from "@/components/PageLayout"
import SE<PERSON><PERSON> from "@/components/SEOHead"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

const ContactPage = () => {
  return (
    <>
      <SEOHead 
        title="Contact Us - Cursive Font Generator"
        description="Get in touch with Cursive Font Generator team. Send questions, feedback, or suggestions. We typically respond within 24 hours."
        canonical="https://cursivefontgenerator.top/contact"
        keywords="contact cursive font generator, support, help, feedback, questions"
      />
      <PageLayout 
        title="Contact Us" 
        breadcrumbs={[{ label: "Contact Us" }]}
      >
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Introduction */}
        <div className="text-center space-y-4">
          <p className="text-lg text-muted-foreground">
            We'd love to hear from you! If you have any questions, suggestions, or feedback, please feel free to contact us.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Contact Methods */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">Get in Touch</h2>
            
            <div className="space-y-4">
              <Card>
                <CardContent className="flex items-center p-6 space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Mail className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Email</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center p-6 space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <MessageCircle className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Live Chat</h3>
                    <p className="text-muted-foreground">Monday to Friday 9:00-18:00</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center p-6 space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Response Time</h3>
                    <p className="text-muted-foreground">Usually within 24 hours</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle>Send Message</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Name</label>
                  <Input placeholder="Enter your name" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Email</label>
                  <Input type="email" placeholder="Enter your email" />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Subject</label>
                <Input placeholder="Enter message subject" />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Message</label>
                <Textarea 
                  placeholder="Describe your question or suggestion..." 
                  rows={6}
                />
              </div>
              
              <Button className="w-full">
                Send Message
              </Button>
              
              <p className="text-xs text-muted-foreground text-center">
                By submitting this form, you agree to our <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Support Categories */}
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">How Can We Help You?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Choose the category that best describes your inquiry for faster assistance.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold">Technical Support</h3>
                <p className="text-sm text-muted-foreground">
                  Issues with font generation, copying, or platform compatibility
                </p>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Font not displaying correctly</li>
                  <li>• Copy function not working</li>
                  <li>• Mobile compatibility issues</li>
                  <li>• Browser-specific problems</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold">Feature Requests</h3>
                <p className="text-sm text-muted-foreground">
                  Suggestions for new fonts, features, or improvements
                </p>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• New font style requests</li>
                  <li>• Feature enhancement ideas</li>
                  <li>• User interface improvements</li>
                  <li>• Integration suggestions</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <CardContent className="space-y-4">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold">Business Inquiries</h3>
                <p className="text-sm text-muted-foreground">
                  Partnership opportunities, API access, or commercial licensing
                </p>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• API integration requests</li>
                  <li>• Partnership opportunities</li>
                  <li>• Commercial licensing</li>
                  <li>• Custom solutions</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Comprehensive FAQ */}
        <div className="bg-muted/50 rounded-lg p-8">
          <h2 className="text-2xl font-bold mb-6 text-center">Comprehensive FAQ</h2>
          <div className="space-y-8">

            {/* General Questions */}
            <div>
              <h3 className="text-xl font-semibold mb-4 text-primary">General Questions</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold">How do I use the font generator?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Simply enter your text in the input box, browse through our font styles, and click the "Copy" button
                    next to your preferred style. The formatted text is instantly copied to your clipboard and ready to paste anywhere.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Is the service completely free?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Yes! Our core font generation service is completely free with no registration required. You can generate
                    unlimited text in all available font styles without any hidden fees or subscription costs.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Do I need to create an account?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    No account is required for basic font generation. You can start using our service immediately.
                    We may introduce optional accounts in the future for advanced features like saving favorites.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">How many fonts can I generate?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    There are no limits! You can generate as many different font styles as you want, convert as much text
                    as needed, and use our service as frequently as you like.
                  </p>
                </div>
              </div>
            </div>

            {/* Technical Questions */}
            <div>
              <h3 className="text-xl font-semibold mb-4 text-primary">Technical Questions</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold">Where can I use the generated fonts?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Our Unicode-based fonts work on virtually all platforms including Instagram, Facebook, Twitter, WhatsApp,
                    Discord, Google Docs, Microsoft Word, and most modern applications that support Unicode text.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Why aren't fonts displaying correctly?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Font display issues are usually due to limited Unicode support on older devices or applications.
                    Try using a different device or app, or contact us with specific details about where you're experiencing issues.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Do fonts work on mobile devices?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Yes! Our fonts are fully compatible with modern mobile devices including iPhones, Android phones, and tablets.
                    They work in mobile browsers, apps, and messaging platforms.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Can I use fonts in design software?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Absolutely! You can copy our generated fonts and paste them into design software like Photoshop,
                    Illustrator, Canva, Figma, and other applications that support text input.
                  </p>
                </div>
              </div>
            </div>

            {/* Business & Commercial Use */}
            <div>
              <h3 className="text-xl font-semibold mb-4 text-primary">Business & Commercial Use</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold">Can I use fonts for commercial projects?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Yes! Our generated fonts can be used for both personal and commercial projects including business branding,
                    marketing materials, social media content, and client work without additional licensing fees.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Do you offer API access?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    We're currently developing API access for developers and businesses who want to integrate our font
                    generation capabilities into their applications. Contact us for early access information.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Are there enterprise solutions available?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    We offer custom solutions for enterprises including white-label options, dedicated support,
                    and custom font development. Contact our business team to discuss your specific requirements.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Can you create custom fonts?</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Yes! We offer custom font development services for businesses and organizations with specific branding needs.
                    Contact us to discuss your custom font requirements and pricing.
                  </p>
                </div>
              </div>
            </div>

            {/* Troubleshooting */}
            <div>
              <h3 className="text-xl font-semibold mb-4 text-primary">Troubleshooting</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold">The copy button isn't working</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    This is usually a browser permission issue. Try manually selecting and copying the text,
                    or check if your browser is blocking clipboard access. Refresh the page and try again.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Fonts look different on different platforms</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    This is normal! Different platforms may render Unicode characters slightly differently.
                    The core style remains the same, but minor visual variations are expected across platforms.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">Some characters are missing or replaced</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Not all Unicode characters have stylized equivalents. Numbers, punctuation, and special characters
                    may display in regular format. This is a limitation of the Unicode character set.
                  </p>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold">The website is loading slowly</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Our website is optimized for speed, but slow loading may be due to your internet connection or device.
                    Try refreshing the page or clearing your browser cache. Contact us if problems persist.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Tips */}
        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 text-blue-800">💡 Tips for Faster Support</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <ul className="space-y-2 text-sm text-blue-700">
              <li>• Include your browser and device information</li>
              <li>• Describe the exact steps you took</li>
              <li>• Mention which font style you were using</li>
              <li>• Include screenshots if possible</li>
            </ul>
            <ul className="space-y-2 text-sm text-blue-700">
              <li>• Tell us which platform you're trying to use the font on</li>
              <li>• Check our FAQ before contacting us</li>
              <li>• Be specific about error messages</li>
              <li>• Let us know if the issue is consistent or intermittent</li>
            </ul>
          </div>
        </div>
      </div>
    </PageLayout>
    </>
  )
}

export default ContactPage 