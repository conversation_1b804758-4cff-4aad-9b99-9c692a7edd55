import React from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Clock, Globe, BookOpen, Crown, Scroll, Palette, Users, Star } from 'lucide-react';

const TypographyHistoryCulture: React.FC = () => {
  const historicalPeriods = [
    {
      period: "Ancient Origins (3200 BCE - 500 CE)",
      icon: Scroll,
      color: "amber",
      description: "The birth of written communication",
      developments: [
        "Cuneiform script in Mesopotamia (3200 BCE)",
        "Egyptian hieroglyphs and hieratic script",
        "Phoenician alphabet - ancestor of modern letters",
        "Greek alphabet with vowels",
        "Roman capitals (<PERSON><PERSON><PERSON>'s Column, 113 CE)"
      ],
      culturalImpact: "Writing systems enabled the preservation of knowledge, laws, and literature, forming the foundation of civilization.",
      modernInfluence: "Roman capitals still influence modern typography, especially in formal and institutional contexts."
    },
    {
      period: "Medieval Era (500 - 1450 CE)",
      icon: Crown,
      color: "purple",
      description: "Manuscripts and religious typography",
      developments: [
        "Uncial and half-uncial scripts",
        "Carolingian minuscule (8th century)",
        "Gothic/Blackletter scripts (12th century)",
        "Illuminated manuscripts",
        "Development of lowercase letters"
      ],
      culturalImpact: "Typography became intertwined with religious and royal power, with elaborate scripts showing status and devotion.",
      modernInfluence: "Gothic fonts remain popular for formal documents, certificates, and traditional branding."
    },
    {
      period: "Renaissance & Print (1450 - 1800)",
      icon: BookOpen,
      color: "blue",
      description: "Gutenberg's revolution and humanist typography",
      developments: [
        "Gutenberg's printing press (1440s)",
        "Humanist typefaces (Jenson, 1470)",
        "Italic type by Aldus Manutius (1501)",
        "Garamond and Caslon typefaces",
        "Standardization of letterforms"
      ],
      culturalImpact: "Mass production of books democratized knowledge, fueling the Renaissance, Reformation, and Scientific Revolution.",
      modernInfluence: "Many classic typefaces from this era (Garamond, Caslon) are still widely used in modern publishing."
    },
    {
      period: "Industrial Age (1800 - 1950)",
      icon: Users,
      color: "green",
      description: "Mass communication and advertising typography",
      developments: [
        "Sans-serif typefaces emerge",
        "Slab serif fonts for advertising",
        "Phototypesetting technology",
        "Art Nouveau and Art Deco influences",
        "Bauhaus movement and modernist typography"
      ],
      culturalImpact: "Typography became a tool for mass marketing, political propaganda, and artistic expression in urban environments.",
      modernInfluence: "Industrial-era fonts like Helvetica and Futura remain cornerstones of modern graphic design."
    },
    {
      period: "Digital Revolution (1950 - Present)",
      icon: Palette,
      color: "red",
      description: "Computer fonts and global typography",
      developments: [
        "Digital typesetting systems",
        "PostScript and TrueType fonts",
        "Web fonts and CSS typography",
        "Unicode standard for global scripts",
        "Variable fonts and responsive typography"
      ],
      culturalImpact: "Digital technology democratized font creation and enabled global communication across all writing systems.",
      modernInfluence: "Today's typography landscape includes thousands of fonts accessible to anyone with a computer or smartphone."
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      amber: "from-amber-50 to-yellow-50 border-amber-200",
      purple: "from-purple-50 to-violet-50 border-purple-200",
      blue: "from-blue-50 to-indigo-50 border-blue-200",
      green: "from-green-50 to-emerald-50 border-green-200",
      red: "from-red-50 to-pink-50 border-red-200"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColor = (color: string) => {
    const colorMap = {
      amber: "text-amber-500",
      purple: "text-purple-500",
      blue: "text-blue-500",
      green: "text-green-500",
      red: "text-red-500"
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Typography History & Culture: Evolution of Fonts Through Time"
        description="Explore the fascinating history of typography from ancient scripts to digital fonts. Discover how culture, technology, and society shaped the fonts we use today."
        keywords="typography history, font evolution, writing systems, cultural typography, font development, typography culture, ancient scripts"
        canonical="https://cursivefontgenerator.com/typography-history-culture"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-amber-900 via-orange-900 to-red-900 text-white">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Clock className="h-8 w-8 text-amber-400 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold">
                  Typography Through Time
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Journey through 5,000 years of typography history. Discover how fonts evolved from 
                ancient cave paintings to digital screens, shaped by culture, technology, and human creativity.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">The Cultural DNA of Typography</h2>
                  <div className="prose prose-lg max-w-none">
                    <p className="mb-4 leading-relaxed">
                      Typography is far more than just letters on a page—it's a reflection of human civilization 
                      itself. Every font tells a story of the culture that created it, the technology available 
                      at the time, and the social needs it was designed to meet. From the monumental inscriptions 
                      of ancient Rome to the pixel-perfect fonts of the digital age, typography has been shaped 
                      by politics, religion, art, and technology.
                    </p>
                    <p className="mb-4 leading-relaxed">
                      Understanding typography's history helps us appreciate why certain fonts feel formal or 
                      casual, traditional or modern, trustworthy or playful. These associations aren't arbitrary—
                      they're the result of centuries of cultural evolution and collective memory embedded in 
                      our visual language.
                    </p>
                  </div>
                  
                  <div className="grid md:grid-cols-3 gap-6 mt-8">
                    <div className="text-center">
                      <div className="bg-amber-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Globe className="h-8 w-8 text-amber-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Global Heritage</h3>
                      <p className="text-sm text-gray-600">Typography reflects the diversity of human cultures and languages</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <BookOpen className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Knowledge Preservation</h3>
                      <p className="text-sm text-gray-600">Fonts have preserved human knowledge across millennia</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <Palette className="h-8 w-8 text-purple-600" />
                      </div>
                      <h3 className="font-semibold mb-2">Artistic Expression</h3>
                      <p className="text-sm text-gray-600">Typography has always been both functional and beautiful</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Historical Timeline */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-6xl mx-auto space-y-8">
              
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">The Evolution of Typography</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Trace the development of written communication from ancient civilizations 
                  to the digital age, and see how each era contributed to modern typography.
                </p>
              </div>

              <div className="space-y-8">
                {historicalPeriods.map((period, index) => (
                  <Card key={index} className={`bg-gradient-to-br ${getColorClasses(period.color)} border-2`}>
                    <CardContent className="p-8">
                      <div className="flex items-center mb-6">
                        <period.icon className={`h-8 w-8 mr-4 ${getIconColor(period.color)}`} />
                        <div>
                          <h3 className="text-xl font-bold">{period.period}</h3>
                          <p className="text-gray-600">{period.description}</p>
                        </div>
                      </div>

                      <div className="grid lg:grid-cols-3 gap-6">
                        <div>
                          <h4 className="font-semibold mb-3">Key Developments:</h4>
                          <ul className="space-y-2">
                            {period.developments.map((development, i) => (
                              <li key={i} className="flex items-start">
                                <Star className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                                <span className="text-sm text-gray-700">{development}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3">Cultural Impact:</h4>
                          <p className="text-sm text-gray-700 leading-relaxed">
                            {period.culturalImpact}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3">Modern Influence:</h4>
                          <p className="text-sm text-gray-700 leading-relaxed">
                            {period.modernInfluence}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Cultural Perspectives */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 flex items-center">
                    <Globe className="h-6 w-6 mr-3 text-blue-500" />
                    Typography Across Cultures
                  </h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">Eastern Traditions</h3>
                      <div className="space-y-4">
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Chinese Calligraphy</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Over 3,000 years of evolution from oracle bones to modern simplified characters.
                          </p>
                          <div className="text-lg">漢字書法藝術</div>
                        </div>
                        
                        <div className="bg-pink-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Japanese Typography</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Unique blend of three writing systems: Hiragana, Katakana, and Kanji.
                          </p>
                          <div className="text-lg">ひらがな カタカナ 漢字</div>
                        </div>
                        
                        <div className="bg-orange-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Arabic Scripts</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Right-to-left flowing scripts with contextual letter forms.
                          </p>
                          <div className="text-lg">الخط العربي الجميل</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold mb-4">Western Evolution</h3>
                      <div className="space-y-4">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Roman Legacy</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Classical proportions and letterforms that still influence modern design.
                          </p>
                          <div className="text-lg">SENATUS POPULUSQUE ROMANUS</div>
                        </div>
                        
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Gothic Tradition</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Medieval manuscripts with elaborate, vertical letterforms.
                          </p>
                          <div className="text-lg">𝔊𝔬𝔱𝔥𝔦𝔠 ℌ𝔢𝔯𝔦𝔱𝔞𝔤𝔢</div>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Modern Minimalism</h4>
                          <p className="text-sm text-gray-700 mb-2">
                            Clean, functional design influenced by Bauhaus and Swiss typography.
                          </p>
                          <div className="text-lg">Modern Simplicity</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Typography and Social Change</h2>
                  
                  <div className="space-y-6">
                    <div className="border-l-4 border-blue-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">The Printing Revolution</h3>
                      <p className="text-gray-700 mb-3">
                        Gutenberg's printing press didn't just change how books were made—it transformed society. 
                        Standardized letterforms enabled mass literacy, the spread of ideas, and the Protestant Reformation.
                      </p>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-700">
                          <strong>Impact:</strong> The first printed Bible (1455) made religious texts accessible to common people, 
                          challenging the Church's monopoly on knowledge.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Industrial Typography</h3>
                      <p className="text-gray-700 mb-3">
                        The Industrial Revolution brought bold, attention-grabbing fonts designed for advertising 
                        and mass communication in rapidly growing cities.
                      </p>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-700">
                          <strong>Innovation:</strong> Sans-serif fonts emerged to cut through visual noise, 
                          while slab serifs dominated newspaper headlines and posters.
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-purple-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3">Digital Democracy</h3>
                      <p className="text-gray-700 mb-3">
                        Digital technology democratized typography, allowing anyone to create and share fonts. 
                        This led to an explosion of creativity and cultural expression.
                      </p>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-700">
                          <strong>Revolution:</strong> Unicode standard enabled global communication, 
                          supporting over 150 writing systems in a single document.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">The Future of Typography</h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Emerging Technologies</h3>
                      <div className="space-y-3">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Variable Fonts</h4>
                          <p className="text-sm text-gray-700">
                            Single font files that contain multiple styles, enabling responsive 
                            typography that adapts to context.
                          </p>
                        </div>
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">AI-Generated Fonts</h4>
                          <p className="text-sm text-gray-700">
                            Machine learning algorithms creating personalized fonts based on 
                            user preferences and content context.
                          </p>
                        </div>
                        
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Augmented Reality Typography</h4>
                          <p className="text-sm text-gray-700">
                            3D fonts and interactive text that respond to user gestures 
                            and environmental conditions.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-4">Cultural Trends</h3>
                      <div className="space-y-3">
                        <div className="bg-orange-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Inclusive Design</h4>
                          <p className="text-sm text-gray-700">
                            Growing focus on accessibility and fonts that work for users 
                            with diverse abilities and needs.
                          </p>
                        </div>
                        
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Cultural Preservation</h4>
                          <p className="text-sm text-gray-700">
                            Digital efforts to preserve endangered writing systems and 
                            traditional calligraphy styles.
                          </p>
                        </div>
                        
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Sustainable Typography</h4>
                          <p className="text-sm text-gray-700">
                            Eco-friendly fonts designed to reduce energy consumption 
                            and environmental impact.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 md:py-16 bg-gradient-to-r from-amber-600 to-orange-600 text-white">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Be Part of Typography's Future</h2>
              <p className="text-lg mb-6 opacity-90">
                Every font you choose connects you to thousands of years of human creativity and cultural evolution. 
                Explore our collection and add your voice to typography's ongoing story.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/" 
                  className="inline-flex items-center px-6 py-3 bg-white text-amber-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Clock className="h-5 w-5 mr-2" />
                  Explore Historical Fonts
                </a>
                <a 
                  href="/font-inspiration-showcase" 
                  className="inline-flex items-center px-6 py-3 bg-amber-700 text-white font-semibold rounded-lg hover:bg-amber-800 transition-colors"
                >
                  <Palette className="h-5 w-5 mr-2" />
                  See Cultural Examples
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default TypographyHistoryCulture;
