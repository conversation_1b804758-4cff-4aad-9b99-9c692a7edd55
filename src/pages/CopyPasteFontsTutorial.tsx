import React, { useState } from 'react';
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";
import { Card, CardContent } from "@/components/ui/card";
import { Copy, Check, Smartphone, Monitor, Tablet, AlertCircle, PlayCircle, Download } from 'lucide-react';

const CopyPasteFontsTutorial: React.FC = () => {
  const [copiedText, setCopiedText] = useState<string>('');
  const [showSuccess, setShowSuccess] = useState(false);

  const handleCopy = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        setCopiedText('');
      }, 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const demoTexts = [
    { label: 'Cursive Example', text: '𝒯𝒽𝒾𝓈 𝒾𝓈 𝒶 𝒸𝓊𝓇𝓈𝒾𝓋𝑒 𝒻𝑜𝓃𝓉 𝑒𝓍𝒶𝓂𝓅𝓁𝑒' },
    { label: 'Bold Example', text: '𝗧𝗵𝗶𝘀 𝗶𝘀 𝗮 𝗯𝗼𝗹𝗱 𝗳𝗼𝗻𝘁 𝗲𝘅𝗮𝗺𝗽𝗹𝗲' },
    { label: 'Italic Example', text: '𝘛𝘩𝘪𝘴 𝘪𝘴 𝘢𝘯 𝘪𝘵𝘢𝘭𝘪𝘤 𝘧𝘰𝘯𝘵 𝘦𝘹𝘢𝘮𝘱𝘭𝘦' }
  ];

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <SEOHead 
        title="Copy Paste Fonts Tutorial: Complete Guide to Unicode Text 2024"
        description="Master the art of copying and pasting Unicode fonts across all devices and platforms. Step-by-step tutorial with troubleshooting tips and best practices."
        keywords="copy paste fonts, unicode fonts tutorial, how to copy fonts, paste special characters, unicode text copy, font copying guide"
        canonical="https://cursivefontgenerator.com/copy-paste-fonts-tutorial"
      />
      
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-12 md:py-16 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <Copy className="h-8 w-8 text-green-500 mr-3" />
                <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold gradient-text">
                  Copy Paste Fonts Tutorial
                </h1>
              </div>
              <p className="text-xl opacity-90 max-w-3xl mx-auto">
                Master the complete process of copying and pasting Unicode fonts across all devices, 
                platforms, and applications. From basic techniques to advanced troubleshooting.
              </p>
            </div>
          </div>
        </section>

        {/* Interactive Demo */}
        <section className="py-8 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 text-center">Try It Now - Interactive Demo</h2>
                  <div className="space-y-4">
                    {demoTexts.map((demo, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-white rounded-lg border">
                        <div className="flex-1">
                          <p className="text-sm text-gray-600 mb-1">{demo.label}:</p>
                          <p className="text-lg font-medium">{demo.text}</p>
                        </div>
                        <button
                          onClick={() => handleCopy(demo.text, demo.label)}
                          className="ml-4 flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        >
                          {copiedText === demo.label && showSuccess ? (
                            <>
                              <Check className="h-4 w-4 mr-2" />
                              Copied!
                            </>
                          ) : (
                            <>
                              <Copy className="h-4 w-4 mr-2" />
                              Copy
                            </>
                          )}
                        </button>
                      </div>
                    ))}
                  </div>
                  <p className="text-center text-sm text-gray-600 mt-4">
                    Click any "Copy" button above, then paste the text anywhere to see it in action!
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6">
            <div className="max-w-4xl mx-auto space-y-8">
              
              {/* What Are Unicode Fonts */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Understanding Unicode Fonts</h2>
                  <div className="prose prose-lg max-w-none">
                    <p className="mb-4 leading-relaxed">
                      Unicode fonts are special characters that look like different font styles but are actually 
                      unique Unicode symbols. Unlike traditional fonts that require installation, Unicode fonts 
                      can be copied and pasted anywhere because they're built into the Unicode standard that 
                      all modern devices support.
                    </p>
                    <p className="mb-4 leading-relaxed">
                      When you copy a Unicode font, you're not copying formatting—you're copying actual characters. 
                      This means they work across all platforms, apps, and websites without any special software 
                      or font installations.
                    </p>
                  </div>
                  
                  <div className="bg-blue-50 p-6 rounded-lg mt-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <AlertCircle className="h-5 w-5 mr-2 text-blue-500" />
                      Key Advantages of Unicode Fonts
                    </h3>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700">
                      <li>Work on all devices and platforms</li>
                      <li>No font installation required</li>
                      <li>Maintain formatting when copied</li>
                      <li>Compatible with all major applications</li>
                      <li>Display consistently across different systems</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Step-by-Step Tutorial */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Step-by-Step Copy & Paste Guide</h2>
                  
                  <div className="space-y-8">
                    {/* Step 1 */}
                    <div className="border-l-4 border-green-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                        Generate Your Font
                      </h3>
                      <p className="mb-4 text-gray-700">
                        Start by using our <a href="/cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">cursive font generator</a> to create your desired text style.
                        For elegant designs, try our <a href="/elegant-cursive-fonts" className="text-blue-600 hover:text-blue-800 underline">elegant cursive fonts</a>:
                      </p>
                      <ol className="list-decimal pl-6 space-y-2 text-gray-700">
                        <li>Type your text in the input field</li>
                        <li>Browse through available font styles</li>
                        <li>Preview how your text looks in different fonts</li>
                        <li>Choose the style that best fits your needs</li>
                      </ol>
                    </div>

                    {/* Step 2 */}
                    <div className="border-l-4 border-blue-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                        Copy the Text
                      </h3>
                      <p className="mb-4 text-gray-700">
                        There are several ways to copy your generated font:
                      </p>
                      
                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Method 1: Click to Copy</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Click the "Copy" button next to your chosen font</li>
                            <li>Look for the confirmation message</li>
                            <li>Text is now in your clipboard</li>
                          </ul>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Method 2: Manual Selection</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Select the text with your mouse</li>
                            <li>Right-click and choose "Copy"</li>
                            <li>Or use Ctrl+C (Windows) / Cmd+C (Mac)</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* Step 3 */}
                    <div className="border-l-4 border-purple-500 pl-6">
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <span className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                        Paste Anywhere
                      </h3>
                      <p className="mb-4 text-gray-700">
                        Now you can paste your Unicode font in any application:
                      </p>
                      
                      <div className="space-y-4">
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Universal Paste Methods:</h4>
                          <ul className="list-disc pl-6 space-y-2 text-gray-700">
                            <li><strong>Keyboard Shortcut:</strong> Ctrl+V (Windows) / Cmd+V (Mac)</li>
                            <li><strong>Right-Click Menu:</strong> Right-click and select "Paste"</li>
                            <li><strong>Mobile:</strong> Long-press and select "Paste"</li>
                            <li><strong>Touch Devices:</strong> Tap and hold, then select "Paste"</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Platform-Specific Instructions */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Platform-Specific Instructions</h2>
                  
                  <div className="grid md:grid-cols-3 gap-6">
                    {/* Desktop */}
                    <div className="bg-blue-50 p-6 rounded-lg">
                      <div className="flex items-center mb-4">
                        <Monitor className="h-6 w-6 text-blue-500 mr-3" />
                        <h3 className="text-lg font-semibold">Desktop (Windows/Mac)</h3>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">Copy:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Ctrl+C (Windows)</li>
                            <li>Cmd+C (Mac)</li>
                            <li>Right-click → Copy</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Paste:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Ctrl+V (Windows)</li>
                            <li>Cmd+V (Mac)</li>
                            <li>Right-click → Paste</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* Mobile */}
                    <div className="bg-green-50 p-6 rounded-lg">
                      <div className="flex items-center mb-4">
                        <Smartphone className="h-6 w-6 text-green-500 mr-3" />
                        <h3 className="text-lg font-semibold">Mobile (iOS/Android)</h3>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">Copy:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Tap and hold text</li>
                            <li>Select "Copy" from menu</li>
                            <li>Or use copy button if available</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Paste:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Tap and hold in text field</li>
                            <li>Select "Paste" from menu</li>
                            <li>Text appears instantly</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* Tablet */}
                    <div className="bg-purple-50 p-6 rounded-lg">
                      <div className="flex items-center mb-4">
                        <Tablet className="h-6 w-6 text-purple-500 mr-3" />
                        <h3 className="text-lg font-semibold">Tablet (iPad/Android)</h3>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">Copy:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Touch and hold text</li>
                            <li>Drag to select if needed</li>
                            <li>Tap "Copy" in popup menu</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Paste:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Touch and hold destination</li>
                            <li>Tap "Paste" in popup menu</li>
                            <li>Use keyboard shortcut if available</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Troubleshooting */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Troubleshooting Common Issues</h2>
                  
                  <div className="space-y-6">
                    <div className="bg-red-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4 text-red-800">Problem: Fonts Don't Display Correctly</h3>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">Possible Causes:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Outdated device or browser</li>
                            <li>Limited Unicode support</li>
                            <li>Application-specific restrictions</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">Solutions:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Update your browser or app</li>
                            <li>Try a different application</li>
                            <li>Use simpler Unicode fonts</li>
                            <li>Check device compatibility</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4 text-yellow-800">Problem: Copy Function Not Working</h3>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">Quick Fixes:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Refresh the page and try again</li>
                            <li>Check browser permissions for clipboard access</li>
                            <li>Use manual selection and Ctrl+C/Cmd+C</li>
                            <li>Try a different browser</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-blue-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4 text-blue-800">Problem: Fonts Look Different After Pasting</h3>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">This is Normal Because:</h4>
                          <ul className="list-disc pl-6 space-y-1 text-sm text-gray-700">
                            <li>Different apps may render Unicode slightly differently</li>
                            <li>Font rendering varies between operating systems</li>
                            <li>Screen resolution affects appearance</li>
                            <li>The characters are still the same Unicode symbols</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Best Practices */}
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6">Best Practices & Pro Tips</h2>
                  
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4 text-green-600">✅ Do's</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span>Test fonts in your target application before using extensively</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span>Keep a backup of your original text</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span>Use fonts consistently across your content</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span>Check readability on different devices</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          <span>Save frequently used fonts for quick access</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-xl font-semibold mb-4 text-red-600">❌ Don'ts</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          <span>Don't use fonts that are hard to read</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          <span>Avoid mixing too many different font styles</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          <span>Don't assume all platforms support all Unicode characters</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          <span>Avoid using fonts for critical information only</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          <span>Don't forget to consider accessibility</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <Card className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-bold mb-4">Ready to Master Copy & Paste Fonts?</h2>
                  <p className="text-lg mb-6 opacity-90">
                    Start practicing with our font generators and become a Unicode font expert!
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a
                      href="/cursive-fonts"
                      className="inline-flex items-center px-6 py-3 bg-white text-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <PlayCircle className="h-5 w-5 mr-2" />
                      Try Cursive Font Generator
                    </a>
                    <a
                      href="/elegant-cursive-fonts"
                      className="inline-flex items-center px-6 py-3 bg-green-700 text-white font-semibold rounded-lg hover:bg-green-800 transition-colors"
                    >
                      <Copy className="h-5 w-5 mr-2" />
                      Start with Elegant Cursive Fonts
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default CopyPasteFontsTutorial;
