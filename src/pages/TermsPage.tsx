import PageLayout from "@/components/PageLayout"
import <PERSON><PERSON><PERSON> from "@/components/SEOHead"
import { Card, CardContent } from "@/components/ui/card"

const TermsPage = () => {
  return (
    <>
      <SEOHead
        title="Terms of Service - Cursive Font Generator"
        description="Read our terms of service to understand the rules and conditions for using Cursive Font Generator. Learn about usage rights, responsibilities, and service policies."
        canonical="https://cursivefontgenerator.top/terms"
        keywords="terms of service, usage rules, service agreement, user agreement, cursive font generator"
      />
      <PageLayout
        title="Terms of Service"
        breadcrumbs={[{ label: "Terms of Service" }]}
      >
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <p className="text-muted-foreground">
            Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}
          </p>
          <p className="text-lg text-muted-foreground">
            Welcome to Cursive Font Generator. By using our service, you agree to the following terms.
          </p>
        </div>

        <div className="space-y-6">
          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">1. Service Description</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>Cursive Font Generator is an online text styling tool that provides the following services:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Convert plain text into various font styles</li>
                  <li>Provide text preview functionality</li>
                  <li>Support one-click copying of converted text</li>
                  <li>Provide symbol and special character library</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">2. Usage Rules</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>When using our service, you agree to:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Not use the service for illegal or harmful purposes</li>
                  <li>Not attempt to damage or interfere with the normal operation of the service</li>
                  <li>Not post malicious, defamatory, or infringing content</li>
                  <li>Respect others' intellectual property rights</li>
                  <li>Not conduct automated or bulk requests (unless authorized)</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">3. Intellectual Property</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>Regarding intellectual property:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>The website's design, code, and content belong to us</li>
                  <li>Your original input content belongs to you</li>
                  <li>Converted text styles can be used freely</li>
                  <li>You may not copy or imitate our service</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">4. Disclaimer</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>Please note the following disclaimer:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Service is provided "as is" with no guarantee of being error-free or uninterrupted</li>
                  <li>We are not responsible for any losses caused by using the service</li>
                  <li>We do not guarantee that conversion results will display properly on all platforms</li>
                  <li>We reserve the right to modify or terminate the service at any time</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">5. Privacy Protection</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>We are committed to protecting your privacy:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>We do not store your input text content</li>
                  <li>We only collect necessary statistical information</li>
                  <li>We will not sell your personal information to third parties</li>
                  <li>For details, please see our <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a></li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">6. Service Changes</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>We reserve the following rights:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Modify or update service features at any time</li>
                  <li>Temporarily or permanently discontinue the service</li>
                  <li>Update these terms of service</li>
                  <li>Restrict access for certain users</li>
                </ul>
                <p>We will notify users in advance of significant changes.</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">7. Account Termination</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>We may terminate your access in the following situations:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Violation of these terms of service</li>
                  <li>Engaging in activities that may damage the service</li>
                  <li>Long-term inactivity</li>
                  <li>As required by law</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">8. Dispute Resolution</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>In case of disputes:</p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>First attempt to resolve through friendly negotiation</li>
                  <li>If necessary, resolve through arbitration or legal means</li>
                  <li>Governed by applicable laws</li>
                  <li>Disputes subject to jurisdiction of courts in the website's location</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 space-y-4">
              <h2 className="text-xl font-bold">9. Contact Us</h2>
              <div className="space-y-3 text-muted-foreground">
                <p>
                  If you have any questions about these terms, please contact us through:
                </p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Email: <EMAIL></li>
                  <li>Contact page: <a href="/contact" className="text-primary hover:underline">Contact Us</a></li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <div className="bg-muted/50 rounded-lg p-6 text-center">
            <p className="text-muted-foreground">
              By continuing to use our service, you indicate that you have read, understood, and agree to be bound by these terms.
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
    </>
  )
}

export default TermsPage 