import React, { Suspense, useState } from 'react';
import Header from "@/components/Header"
import HeroSection from "@/components/HeroSection"
import FontGenerator from "@/components/FontGenerator"
import PreviewSection from "@/components/PreviewSection"
import SEOHead from "@/components/SEOHead"

const FeaturesSection = React.lazy(() => import('@/components/FeaturesSection'));
const WhatIsSection = React.lazy(() => import('@/components/WhatIsSection'));
const SymbolLibrary = React.lazy(() => import('@/components/SymbolLibrary'));
const ContentHub = React.lazy(() => import('@/components/ContentHub'));
const Footer = React.lazy(() => import('@/components/Footer'));

function LoadingFallback() {
  return <div className="text-center p-12">Loading...</div>;
}

function HomePage() {
  // Shared state for FontGenerator and PreviewSection
  const [previewText, setPreviewText] = useState("Your stylish text here");
  const [previewStyle, setPreviewStyle] = useState("script");

  // Function to handle preview action from FontGenerator
  const handlePreview = (text: string, styleName: string) => {
    setPreviewText(text);
    
    // Map font style names to preview style names
    const styleMapping: Record<string, string> = {
      "Cursive": "script",
      "Cursive Bold": "script", 
      "Elegant Script": "script",
      "Handwriting Style": "script",
      "Calligraphy Script": "script",
      "Gothic Cursive": "medieval",
      "Old English Script": "medieval",
      "Tattoo Script": "medieval",
      "Bold": "bold",
      "Italic": "italic",
      "Small Caps": "small",
      "Vaporwave": "vaporwave"
    };
    
    setPreviewStyle(styleMapping[styleName] || "script");
    
    // Scroll to PreviewSection
    setTimeout(() => {
      document.getElementById('preview-section')?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }, 100);
  };

  const homePageStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Cursive Font Generator",
    "description": "Generate beautiful cursive, script, and tattoo fonts instantly. Copy and paste stylish text for Instagram, TikTok, and social media designs.",
    "url": "https://cursivefontgenerator.top/",
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "All",
    "browserRequirements": "Requires JavaScript and modern browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Cursive font generation",
      "Tattoo font styles", 
      "Copy and paste functionality",
      "Real-time preview",
      "Symbol library",
      "Mobile responsive"
    ]
  };

  return (
    <div className="flex min-h-screen flex-col">
      <SEOHead 
        structuredData={homePageStructuredData}
      />
      <Header />
      <main className="flex-1">
        <HeroSection onPreview={handlePreview} />
        <FontGenerator onPreview={handlePreview} />
        <PreviewSection 
          initialText={previewText}
          initialStyle={previewStyle}
        />
        <Suspense fallback={<LoadingFallback />}>
          <FeaturesSection />
          <WhatIsSection />
          <SymbolLibrary />
          <ContentHub />
        </Suspense>
      </main>
      <Suspense fallback={null}>
        <Footer />
      </Suspense>
    </div>
  )
}

export default HomePage