/**
 * **IMPORTANT** Never break the original structure, only add highly customized new styles
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 267 83% 98%;
    --foreground: 267 33% 18%;
    --card: 0 0% 100%;
    --card-foreground: 267 33% 18%;
    --popover: 0 0% 100%;
    --popover-foreground: 267 33% 18%;
    --primary: 267 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 267 83% 96%;
    --secondary-foreground: 267 47% 20%;
    --muted: 267 40% 96.1%;
    --muted-foreground: 267 16% 47%;
    --accent: 267 83% 96%;
    --accent-foreground: 267 47% 20%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 267 32% 91%;
    --input: 267 32% 91%;
    --ring: 267 83% 53%;
    --chart-1: 283 76% 61%;
    --chart-2: 199 58% 39%;
    --chart-3: 333 67% 54%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.7rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 267 33% 7%;
    --foreground: 267 31% 91%;
    --card: 267 33% 9%;
    --card-foreground: 267 31% 91%;
    --popover: 267 33% 9%;
    --popover-foreground: 267 31% 91%;
    --primary: 267 77% 63%;
    --primary-foreground: 222 47% 11%;
    --secondary: 267 47% 15%;
    --secondary-foreground: 210 40% 98%;
    --muted: 267 47% 15%;
    --muted-foreground: 267 20% 65%;
    --accent: 267 47% 15%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 267 34% 18%;
    --input: 267 34% 18%;
    --ring: 267 73% 65%;
    --chart-1: 283 70% 60%;
    --chart-2: 199 60% 45%;
    --chart-3: 333 67% 54%;
    --chart-4: 30 80% 55%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-image: 
      radial-gradient(at 30% 10%, hsla(267, 70%, 96%, 0.8) 0px, transparent 50%),
      radial-gradient(at 80% 80%, hsla(290, 70%, 96%, 0.7) 0px, transparent 50%),
      radial-gradient(at 60% 40%, hsla(250, 70%, 97%, 0.6) 0px, transparent 50%);
    background-attachment: fixed;
  }
}

/* Custom styles for font generator application */
.font-generator-results {
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary) transparent;
}

.font-generator-results::-webkit-scrollbar {
  width: 6px;
}

.font-generator-results::-webkit-scrollbar-track {
  background: transparent;
}

.font-generator-results::-webkit-scrollbar-thumb {
  background-color: hsl(var(--primary) / 0.5);
  border-radius: 20px;
}

.symbol-item {
  transition: all 0.3s ease;
}

.symbol-item:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.3);
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 5s ease-in-out infinite;
}

@keyframes pulse-slow {
  0% {
    opacity: 0.95;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.95;
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(138, 43, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(138, 43, 226, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(138, 43, 226, 0.3);
  }
}

.gradient-text {
  background: linear-gradient(to right, hsl(267, 83%, 53%), hsl(283, 76%, 61%));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.preview-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.preview-header {
  height: 32px;
  background: #f0f0f0;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.preview-dot {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.preview-content {
  background: white;
  padding: 16px;
  overflow-y: auto;
}

.social-preview-container {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: white;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.social-preview-container:hover {
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.15);
}

.font-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.font-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(138, 43, 226, 0.12);
}

/* Fancy animations for buttons */
.fancy-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.fancy-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.5s, opacity 0.5s;
}

.fancy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.15);
}

.fancy-button:hover::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.fancy-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(138, 43, 226, 0.1);
}

/* Hero section background animation */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

.animated-background span {
  position: absolute;
  display: block;
  pointer-events: none;
  width: 20px;
  height: 20px;
  background: rgba(138, 43, 226, 0.1);
  animation: animate 25s linear infinite;
  bottom: -150px;
  border-radius: 50%;
}

.animated-background span:nth-child(1) {
  left: 25%;
  width: 80px;
  height: 80px;
  animation-delay: 0s;
}

.animated-background span:nth-child(2) {
  left: 10%;
  width: 20px;
  height: 20px;
  animation-delay: 2s;
  animation-duration: 12s;
}

.animated-background span:nth-child(3) {
  left: 70%;
  width: 20px;
  height: 20px;
  animation-delay: 4s;
}

.animated-background span:nth-child(4) {
  left: 40%;
  width: 60px;
  height: 60px;
  animation-delay: 0s;
  animation-duration: 18s;
}

.animated-background span:nth-child(5) {
  left: 65%;
  width: 20px;
  height: 20px;
  animation-delay: 0s;
}

.animated-background span:nth-child(6) {
  left: 75%;
  width: 110px;
  height: 110px;
  animation-delay: 3s;
}

.animated-background span:nth-child(7) {
  left: 35%;
  width: 150px;
  height: 150px;
  animation-delay: 7s;
}

.animated-background span:nth-child(8) {
  left: 50%;
  width: 25px;
  height: 25px;
  animation-delay: 15s;
  animation-duration: 45s;
}

.animated-background span:nth-child(9) {
  left: 20%;
  width: 15px;
  height: 15px;
  animation-delay: 2s;
  animation-duration: 35s;
}

.animated-background span:nth-child(10) {
  left: 85%;
  width: 150px;
  height: 150px;
  animation-delay: 0s;
  animation-duration: 11s;
}

.animated-background span:nth-child(11) {
  left: 15%;
  width: 120px;
  height: 120px;
  animation-delay: 8s;
  animation-duration: 16s;
}

.animated-background span:nth-child(12) {
  left: 60%;
  width: 90px;
  height: 90px;
  animation-delay: 5s;
  animation-duration: 22s;
}

@keyframes animate {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-1000px) rotate(720deg);
    opacity: 0;
  }
}