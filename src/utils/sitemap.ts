export interface SitemapUrl {
  loc: string
  lastmod: string
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: string
}

export const generateSitemap = (): string => {
  const baseUrl = 'https://cursivefontgenerator.top'
  const currentDate = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
  
  const urls: SitemapUrl[] = [
    {
      loc: baseUrl,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: '1.0'
    },
    // 新增的字体详情页
    {
      loc: `${baseUrl}/cursive-fonts`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.9'
    },
    {
      loc: `${baseUrl}/tattoo-fonts`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.9'
    },
    // 现有页面
    {
      loc: `${baseUrl}/about`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: '0.8'
    },
    {
      loc: `${baseUrl}/contact`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: '0.7'
    },
    {
      loc: `${baseUrl}/privacy`,
      lastmod: currentDate,
      changefreq: 'yearly',
      priority: '0.5'
    },
    {
      loc: `${baseUrl}/terms`,
      lastmod: currentDate,
      changefreq: 'yearly',
      priority: '0.5'
    }
  ]

  const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.map(url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n')}
</urlset>`

  return xmlContent
}

export const generateRobotsTxt = (): string => {
  return `User-agent: *
Allow: /

# Allow all major search engines
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: DuckDuckBot
Allow: /

User-agent: Baiduspider
Allow: /

# Disallow admin or private directories if any
Disallow: /admin/
Disallow: /private/
Disallow: /.env
Disallow: /config/

# Allow access to CSS, JS, and image files
Allow: /src/
Allow: /assets/
Allow: *.css
Allow: *.js
Allow: *.png
Allow: *.jpg
Allow: *.jpeg
Allow: *.gif
Allow: *.svg
Allow: *.webp

# Sitemap location
Sitemap: https://cursivefontgenerator.top/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1`
} 