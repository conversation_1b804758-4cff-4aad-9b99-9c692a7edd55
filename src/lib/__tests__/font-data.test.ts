import { describe, it, expect } from 'vitest'
import { fontStyles, FontStyle } from '../font-data'

describe('Font Data', () => {
  describe('fontStyles array', () => {
    it('should not be empty', () => {
      expect(fontStyles).toBeDefined()
      expect(fontStyles.length).toBeGreaterThan(0)
    })

    it('should contain valid FontStyle objects', () => {
      fontStyles.forEach((font) => {
        expect(font).toHaveProperty('name')
        expect(font).toHaveProperty('transform')
        expect(font).toHaveProperty('category')
        
        expect(typeof font.name).toBe('string')
        expect(typeof font.transform).toBe('function')
        expect(['cursive', 'tattoo', 'other']).toContain(font.category)
        
        if (font.description) {
          expect(typeof font.description).toBe('string')
        }
      })
    })

    it('should have unique font names', () => {
      const names = fontStyles.map(font => font.name)
      const uniqueNames = new Set(names)
      expect(names.length).toBe(uniqueNames.size)
    })
  })

  describe('Font categories', () => {
    it('should have cursive fonts', () => {
      const cursiveFonts = fontStyles.filter(font => font.category === 'cursive')
      expect(cursiveFonts.length).toBeGreaterThan(0)
    })

    it('should have tattoo fonts', () => {
      const tattooFonts = fontStyles.filter(font => font.category === 'tattoo')
      expect(tattooFonts.length).toBeGreaterThan(0)
    })

    it('should include expected cursive fonts', () => {
      const cursiveFontNames = fontStyles
        .filter(font => font.category === 'cursive')
        .map(font => font.name)
      
      expect(cursiveFontNames).toContain('Cursive')
      expect(cursiveFontNames).toContain('Cursive Bold')
    })

    it('should include expected tattoo fonts', () => {
      const tattooFontNames = fontStyles
        .filter(font => font.category === 'tattoo')
        .map(font => font.name)
      
      expect(tattooFontNames).toContain('Gothic Cursive')
      expect(tattooFontNames).toContain('Old English Script')
    })
  })

  describe('Font transformations', () => {
    it('should transform text correctly for Cursive font', () => {
      const cursiveFont = fontStyles.find(font => font.name === 'Cursive')
      expect(cursiveFont).toBeDefined()
      
      if (cursiveFont) {
        const result = cursiveFont.transform('Hello')
        expect(result).toBeDefined()
        expect(typeof result).toBe('string')
        expect(result.length).toBeGreaterThan(0)
        // The transformed text should be different from the input
        expect(result).not.toBe('Hello')
      }
    })

    it('should transform text correctly for Gothic Cursive font', () => {
      const gothicFont = fontStyles.find(font => font.name === 'Gothic Cursive')
      expect(gothicFont).toBeDefined()
      
      if (gothicFont) {
        const result = gothicFont.transform('Test')
        expect(result).toBeDefined()
        expect(typeof result).toBe('string')
        expect(result.length).toBeGreaterThan(0)
        expect(result).not.toBe('Test')
      }
    })

    it('should handle empty string input', () => {
      fontStyles.forEach((font) => {
        const result = font.transform('')
        expect(result).toBeDefined()
        expect(typeof result).toBe('string')
      })
    })

    it('should handle single character input', () => {
      fontStyles.forEach((font) => {
        const result = font.transform('A')
        expect(result).toBeDefined()
        expect(typeof result).toBe('string')
        expect(result.length).toBeGreaterThan(0)
      })
    })

    it('should handle special characters', () => {
      const testChars = ['!', '@', '#', '$', '%', '&', '*']
      
      fontStyles.forEach((font) => {
        testChars.forEach((char) => {
          const result = font.transform(char)
          expect(result).toBeDefined()
          expect(typeof result).toBe('string')
        })
      })
    })

    it('should handle numbers', () => {
      const testNumbers = '0123456789'
      
      fontStyles.forEach((font) => {
        const result = font.transform(testNumbers)
        expect(result).toBeDefined()
        expect(typeof result).toBe('string')
        expect(result.length).toBeGreaterThan(0)
      })
    })

    it('should handle mixed case letters', () => {
      const testText = 'AbCdEfGhIjKlMnOpQrStUvWxYz'
      
      fontStyles.forEach((font) => {
        const result = font.transform(testText)
        expect(result).toBeDefined()
        expect(typeof result).toBe('string')
        expect(result.length).toBeGreaterThan(0)
      })
    })
  })

  describe('Font descriptions', () => {
    it('should have descriptions for key fonts', () => {
      const cursiveFont = fontStyles.find(font => font.name === 'Cursive')
      const boldCursiveFont = fontStyles.find(font => font.name === 'Cursive Bold')
      const gothicFont = fontStyles.find(font => font.name === 'Gothic Cursive')
      
      expect(cursiveFont?.description).toBeDefined()
      expect(boldCursiveFont?.description).toBeDefined()
      expect(gothicFont?.description).toBeDefined()
    })

    it('should have meaningful descriptions', () => {
      fontStyles.forEach((font) => {
        if (font.description) {
          expect(font.description.length).toBeGreaterThan(10)
          expect(font.description).toMatch(/[a-zA-Z]/)
        }
      })
    })
  })

  describe('Font consistency', () => {
    it('should maintain consistent output for same input', () => {
      const testText = 'Consistency Test'
      
      fontStyles.forEach((font) => {
        const result1 = font.transform(testText)
        const result2 = font.transform(testText)
        expect(result1).toBe(result2)
      })
    })

    it('should produce different outputs for different fonts', () => {
      const testText = 'Different Fonts Test'
      const results = fontStyles.map(font => font.transform(testText))
      
      // Most results should be different (allowing for some edge cases)
      const uniqueResults = new Set(results)
      expect(uniqueResults.size).toBeGreaterThan(fontStyles.length * 0.5)
    })
  })

  describe('Performance', () => {
    it('should transform text quickly', () => {
      const testText = 'Performance test with longer text to ensure transformation speed'
      
      fontStyles.forEach((font) => {
        const startTime = performance.now()
        font.transform(testText)
        const endTime = performance.now()
        
        // Transformation should complete within 10ms
        expect(endTime - startTime).toBeLessThan(10)
      })
    })
  })
})
