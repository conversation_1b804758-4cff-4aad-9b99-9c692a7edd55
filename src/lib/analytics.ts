// Google Analytics configuration
export const GA_TRACKING_ID = 'G-B6T1H5T16C'

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    })
  }
}

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = ({ action, category, label, value }: {
  action: string
  category: string
  label?: string
  value?: number
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

// Track font generation events
export const trackFontGeneration = (fontName: string) => {
  event({
    action: 'font_generated',
    category: 'engagement',
    label: fontName,
  })
}

// Track text copy events
export const trackTextCopy = (fontName: string) => {
  event({
    action: 'text_copied',
    category: 'engagement',
    label: fontName,
  })
}

// Track page navigation
export const trackPageView = (pageName: string) => {
  event({
    action: 'page_view',
    category: 'navigation',
    label: pageName,
  })
}

// Track symbol copy events
export const trackSymbolCopy = (symbol: string) => {
  event({
    action: 'symbol_copied',
    category: 'engagement',
    label: symbol,
  })
} 