import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { HelmetProvider } from 'react-helmet-async';
import HomePage from "@/pages/HomePage";
import AboutPage from "@/pages/AboutPage";
import ContactPage from "@/pages/ContactPage";
import PrivacyPage from "@/pages/PrivacyPage";
import TermsPage from "@/pages/TermsPage";
import SitemapPage from "@/pages/SitemapPage";
import RobotsPage from "@/pages/RobotsPage";
import NotFoundPage from "@/pages/NotFoundPage";
import FontDetailPage from "@/pages/FontDetailPage";
import HowToUseCursiveFontsInstagram from "@/pages/HowToUseCursiveFontsInstagram";
import TattooFontDesignGuide from "@/pages/TattooFontDesignGuide";
import SocialMediaFontTips from "@/pages/SocialMediaFontTips";
import CopyPasteFontsTutorial from "@/pages/CopyPasteFontsTutorial";
import FontInspirationShowcase from "@/pages/FontInspirationShowcase";
import FontGeneratorComparison from "@/pages/FontGeneratorComparison";
import TypographyTrends2024 from "@/pages/TypographyTrends2024";
import FontPsychologyGuide from "@/pages/FontPsychologyGuide";
import BrandFontSelectionGuide from "@/pages/BrandFontSelectionGuide";
import FontAccessibilityGuide from "@/pages/FontAccessibilityGuide";
import TypographyHistoryCulture from "@/pages/TypographyHistoryCulture";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import GoogleAnalytics from "@/components/GoogleAnalytics";
import { pageview } from "@/lib/analytics";

// Component to track route changes
function RouteTracker() {
  const location = useLocation();

  useEffect(() => {
    pageview(location.pathname);
  }, [location]);

  return null;
}

function App() {
  return (
    <HelmetProvider>
      <TooltipProvider>
        <GoogleAnalytics />
        <BrowserRouter>
          <RouteTracker />
          <Routes>
            <Route path="/" element={<HomePage />} />

            {/* 现有页面 */}
            <Route path="/about" element={<AboutPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/privacy" element={<PrivacyPage />} />
            <Route path="/terms" element={<TermsPage />} />
            <Route path="/sitemap.xml" element={<SitemapPage />} />
            <Route path="/robots.txt" element={<RobotsPage />} />

            {/* 字体详情页 - 核心Cursive字体页面 */}
            <Route path="/cursive-fonts" element={<FontDetailPage />} />
            <Route path="/elegant-cursive-fonts" element={<FontDetailPage />} />
            <Route path="/modern-cursive-fonts" element={<FontDetailPage />} />
            <Route path="/vintage-cursive-fonts" element={<FontDetailPage />} />
            <Route path="/romantic-cursive-fonts" element={<FontDetailPage />} />

            {/* 相关字体页面 */}
            <Route path="/script-fonts" element={<FontDetailPage />} />
            <Route path="/elegant-script-fonts" element={<FontDetailPage />} />
            <Route path="/handwriting-fonts" element={<FontDetailPage />} />
            <Route path="/calligraphy-fonts" element={<FontDetailPage />} />

            {/* 其他字体样式页面 */}
            <Route path="/tattoo-fonts" element={<FontDetailPage />} />
            <Route path="/gothic-fonts" element={<FontDetailPage />} />
            <Route path="/old-english-fonts" element={<FontDetailPage />} />
            <Route path="/tattoo-script-fonts" element={<FontDetailPage />} />
            <Route path="/bold-fonts" element={<FontDetailPage />} />
            <Route path="/italic-fonts" element={<FontDetailPage />} />
            <Route path="/small-caps-fonts" element={<FontDetailPage />} />
            <Route path="/vaporwave-fonts" element={<FontDetailPage />} />

            {/* 内容营销页面 */}
            <Route path="/how-to-use-cursive-fonts-instagram" element={<HowToUseCursiveFontsInstagram />} />
            <Route path="/tattoo-font-design-guide" element={<TattooFontDesignGuide />} />
            <Route path="/social-media-font-tips" element={<SocialMediaFontTips />} />
            <Route path="/copy-paste-fonts-tutorial" element={<CopyPasteFontsTutorial />} />
            <Route path="/font-inspiration-showcase" element={<FontInspirationShowcase />} />
            <Route path="/font-generator-comparison" element={<FontGeneratorComparison />} />
            <Route path="/typography-trends-2024" element={<TypographyTrends2024 />} />
            <Route path="/font-psychology-guide" element={<FontPsychologyGuide />} />
            <Route path="/brand-font-selection-guide" element={<BrandFontSelectionGuide />} />
            <Route path="/font-accessibility-guide" element={<FontAccessibilityGuide />} />
            <Route path="/typography-history-culture" element={<TypographyHistoryCulture />} />

            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </BrowserRouter>
        <Toaster />
      </TooltipProvider>
    </HelmetProvider>
  );
}

export default App;
