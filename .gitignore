# =========================================================================
# === .gitignore for Vite + React + TypeScript Projects
# =========================================================================

# -------------------------------------------------------------------------
# Dependencies & Lockfiles
# -------------------------------------------------------------------------
# Ignore node_modules directory
/node_modules

# Lockfiles from different package managers.
# You might want to commit your lockfile (e.g., package-lock.json, pnpm-lock.yaml)
# but ignore lockfiles from other managers if your team standardizes on one.
# yarn.lock
# package-lock.json
# pnpm-lock.yaml


# -------------------------------------------------------------------------
# Build & Distribution output
# -------------------------------------------------------------------------
# Vite's default build output directory
/dist
/dist-ssr

# A common alternative build output directory name
/build

# -------------------------------------------------------------------------
# Caches & Logs
# -------------------------------------------------------------------------
# Vite's cache directory
/.vite

# TypeScript build info cache
*.tsbuildinfo

# Log files from package managers
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# -------------------------------------------------------------------------
# Environment Variables
# -------------------------------------------------------------------------
# Ignore all local environment variable files.
# These files contain sensitive credentials and should NEVER be committed.
.env
.env.local
.env.*.local

# It's a good practice to commit a template/example file.
# The '!' prefix un-ignores a file that was previously ignored.
!/.env.example
!/.env.template

# -------------------------------------------------------------------------
# IDE & Editor specific files
# -------------------------------------------------------------------------
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json
!.vscode/launch.json

# JetBrains (WebStorm, etc.)
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# -------------------------------------------------------------------------
# OS-specific files
# -------------------------------------------------------------------------
# macOS
.DS_Store

# Windows
Thumbs.db

# -------------------------------------------------------------------------
# Testing
# -------------------------------------------------------------------------
/coverage

# -------------------------------------------------------------------------
# Deployment
# -------------------------------------------------------------------------
# Vercel deployment files
.vercel