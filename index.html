<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238A2BE2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 3H7a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h2'%3E%3C/path%3E%3Cpath d='M12 19h3a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2h-3'%3E%3C/path%3E%3Cline x1='8' y1='12' x2='16' y2='12'%3E%3C/line%3E%3C/svg%3E" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cursive Font Generator | Copy Paste Script & Tattoo Fonts</title>
    <meta name="description" content="Generate beautiful cursive, script, and tattoo fonts instantly. Copy and paste stylish text for Instagram, TikTok, and social media designs." />
    <meta name="keywords" content="cursive font generator, tattoo fonts, script fonts, handwriting fonts, copy paste fonts, instagram fonts" />
    <link rel="canonical" href="https://cursivefontgenerator.top/" />
    <meta name="robots" content="index, follow" />
    <meta name="author" content="Cursive Font Generator" />
    <meta name="language" content="en" />
    <meta name="revisit-after" content="7 days" />
    <meta name="theme-color" content="#8A2BE2" />
    <meta name="msapplication-TileColor" content="#8A2BE2" />
    <meta name="application-name" content="Cursive Font Generator" />
    <meta name="apple-mobile-web-app-title" content="Cursive Font Generator" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta property="og:title" content="Cursive Font Generator | Copy Paste Script & Tattoo Fonts" />
    <meta property="og:description" content="Generate beautiful cursive, script, and tattoo fonts instantly. Copy and paste stylish text for Instagram, TikTok, and social media designs." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://cursivefontgenerator.top/" />
    <meta property="og:image" content="https://cursivefontgenerator.top/og-image.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Cursive Font Generator" />
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Cursive Font Generator | Copy Paste Script & Tattoo Fonts" />
    <meta property="twitter:description" content="Generate beautiful cursive, script, and tattoo fonts instantly. Copy and paste stylish text for Instagram, TikTok, and social media designs." />
    <meta property="twitter:image" content="https://cursivefontgenerator.top/og-image.png" />
    <meta property="twitter:site" content="@CursiveFontGen" />
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-B6T1H5T16C"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-B6T1H5T16C');
    </script>
    
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "url": "https://cursivefontgenerator.top/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://cursivefontgenerator.top/?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Cursive Font Generator",
      "url": "https://cursivefontgenerator.top/",
      "logo": "https://cursivefontgenerator.top/logo.svg"
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <!-- IMPORTANT: Never remove the following script reference, otherwise advanced features like element editing will not work -->
    <script src="https://static.devv.ai/devv-app.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>