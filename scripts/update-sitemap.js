#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get current date in YYYY-MM-DD format
const currentDate = new Date().toISOString().split('T')[0];

// Define sitemap URLs with their update frequencies - 与App.tsx路由完全一致
const sitemapUrls = [
  // Main pages
  {
    loc: 'https://cursivefontgenerator.top',
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    loc: 'https://cursivefontgenerator.top/about',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/contact',
    changefreq: 'monthly',
    priority: '0.7'
  },
  {
    loc: 'https://cursivefontgenerator.top/privacy',
    changefreq: 'yearly',
    priority: '0.5'
  },
  {
    loc: 'https://cursivefontgenerator.top/terms',
    changefreq: 'yearly',
    priority: '0.5'
  },

  // Font detail pages - 核心Cursive字体页面（最高优先级）
  {
    loc: 'https://cursivefontgenerator.top/cursive-fonts',
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    loc: 'https://cursivefontgenerator.top/elegant-cursive-fonts',
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    loc: 'https://cursivefontgenerator.top/modern-cursive-fonts',
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    loc: 'https://cursivefontgenerator.top/vintage-cursive-fonts',
    changefreq: 'weekly',
    priority: '0.9'
  },
  {
    loc: 'https://cursivefontgenerator.top/romantic-cursive-fonts',
    changefreq: 'weekly',
    priority: '0.9'
  },

  // 相关字体页面（第二优先级）
  {
    loc: 'https://cursivefontgenerator.top/script-fonts',
    changefreq: 'weekly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/elegant-script-fonts',
    changefreq: 'weekly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/handwriting-fonts',
    changefreq: 'weekly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/calligraphy-fonts',
    changefreq: 'weekly',
    priority: '0.8'
  },

  // 其他字体样式页面（第三优先级）
  {
    loc: 'https://cursivefontgenerator.top/tattoo-fonts',
    changefreq: 'weekly',
    priority: '0.7'
  },
  {
    loc: 'https://cursivefontgenerator.top/gothic-fonts',
    changefreq: 'weekly',
    priority: '0.7'
  },
  {
    loc: 'https://cursivefontgenerator.top/old-english-fonts',
    changefreq: 'weekly',
    priority: '0.7'
  },
  {
    loc: 'https://cursivefontgenerator.top/tattoo-script-fonts',
    changefreq: 'weekly',
    priority: '0.7'
  },
  {
    loc: 'https://cursivefontgenerator.top/bold-fonts',
    changefreq: 'weekly',
    priority: '0.6'
  },
  {
    loc: 'https://cursivefontgenerator.top/italic-fonts',
    changefreq: 'weekly',
    priority: '0.6'
  },
  {
    loc: 'https://cursivefontgenerator.top/small-caps-fonts',
    changefreq: 'weekly',
    priority: '0.6'
  },
  {
    loc: 'https://cursivefontgenerator.top/vaporwave-fonts',
    changefreq: 'weekly',
    priority: '0.6'
  },

  // Content marketing pages
  {
    loc: 'https://cursivefontgenerator.top/how-to-use-cursive-fonts-instagram',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/tattoo-font-design-guide',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/social-media-font-tips',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/copy-paste-fonts-tutorial',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/font-inspiration-showcase',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/font-generator-comparison',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/typography-trends-2024',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/font-psychology-guide',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/brand-font-selection-guide',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/font-accessibility-guide',
    changefreq: 'monthly',
    priority: '0.8'
  },
  {
    loc: 'https://cursivefontgenerator.top/typography-history-culture',
    changefreq: 'monthly',
    priority: '0.8'
  }
];

// Generate sitemap XML content
const generateSitemapXML = () => {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
  const xmlFooter = '</urlset>';
  
  const urlEntries = sitemapUrls.map(url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n');
  
  return `${xmlHeader}\n${urlEntries}\n${xmlFooter}`;
};

// Update sitemap file
const updateSitemap = () => {
  const sitemapPath = path.join(__dirname, '../public/sitemap.xml');
  const sitemapContent = generateSitemapXML();
  
  try {
    fs.writeFileSync(sitemapPath, sitemapContent, 'utf8');
    console.log(`✅ Sitemap updated successfully! Date: ${currentDate}`);
    console.log(`📍 File location: ${sitemapPath}`);
  } catch (error) {
    console.error('❌ Error updating sitemap:', error);
    process.exit(1);
  }
};

// Auto-submit to IndexNow after sitemap update
const autoSubmitToIndexNow = async () => {
  const args = process.argv.slice(2);
  const skipIndexNow = args.includes('--skip-indexnow');

  if (skipIndexNow) {
    console.log('⏭️  Skipping IndexNow submission (--skip-indexnow flag)');
    return;
  }

  try {
    console.log('\n🚀 Auto-submitting to Bing IndexNow...');
    const { spawn } = await import('child_process');

    const indexNowProcess = spawn('node', ['scripts/bing-indexnow.js', '--skip-verification'], {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });

    indexNowProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ IndexNow submission completed successfully!');
      } else {
        console.log('⚠️  IndexNow submission completed with warnings/errors');
      }
    });

    indexNowProcess.on('error', (error) => {
      console.log('⚠️  IndexNow submission failed:', error.message);
    });
  } catch (error) {
    console.log('⚠️  Could not auto-submit to IndexNow:', error.message);
  }
};

// Run the update
console.log('🔄 Updating sitemap.xml...');
console.log(`📊 Total URLs to include: ${sitemapUrls.length}`);
updateSitemap();

// Auto-submit to IndexNow (unless skipped)
autoSubmitToIndexNow();