#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const resetIndexNowKey = () => {
  console.log(`${colors.bold}🔄 INDEXNOW KEY RESET${colors.reset}`);
  console.log('='.repeat(30));
  
  const keyPath = path.join(__dirname, '../public/indexnow-key.txt');
  
  // Backup old key if exists
  if (fs.existsSync(keyPath)) {
    const oldKey = fs.readFileSync(keyPath, 'utf8').trim();
    const backupPath = path.join(__dirname, `../indexnow-key-backup-${Date.now()}.txt`);
    fs.writeFileSync(backupPath, oldKey, 'utf8');
    console.log(`${colors.yellow}📦 Old key backed up to: ${backupPath}${colors.reset}`);
  }
  
  // Generate new key (32 characters, alphanumeric)
  const newKey = crypto.randomBytes(16).toString('hex');
  fs.writeFileSync(keyPath, newKey, 'utf8');
  
  console.log(`${colors.green}🔑 New IndexNow key generated: ${newKey}${colors.reset}`);
  console.log(`${colors.blue}📍 Key file location: ${keyPath}${colors.reset}`);
  console.log(`${colors.blue}🌐 Key file URL: https://cursivefontgenerator.top/indexnow-key.txt${colors.reset}`);
  
  console.log(`\n${colors.bold}📋 NEXT STEPS:${colors.reset}`);
  console.log('1. Deploy this new key file to your website');
  console.log('2. Verify key file is accessible:');
  console.log(`   ${colors.blue}curl https://cursivefontgenerator.top/indexnow-key.txt${colors.reset}`);
  console.log('3. In Bing Webmaster Tools:');
  console.log('   - Go to your site settings');
  console.log('   - Look for IndexNow or API Keys section');
  console.log(`   - Add this key: ${colors.green}${newKey}${colors.reset}`);
  console.log('4. Test IndexNow submission:');
  console.log(`   ${colors.blue}npm run bing-indexnow-test${colors.reset}`);
  
  return newKey;
};

// Run the reset
resetIndexNowKey();
