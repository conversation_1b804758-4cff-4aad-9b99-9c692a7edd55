#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Parse sitemap.xml to extract URLs
const parseSitemap = (sitemapPath) => {
  try {
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
    
    if (!urlMatches) {
      throw new Error('No URLs found in sitemap.xml');
    }
    
    return urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
  } catch (error) {
    console.error(`${colors.red}❌ Error reading sitemap.xml:${colors.reset}`, error.message);
    process.exit(1);
  }
};

// Check if a URL is accessible
const checkUrl = async (url, timeout = 10000) => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(url, {
      method: 'HEAD', // Use HEAD to avoid downloading full content
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SitemapChecker/1.0)'
      }
    });
    
    clearTimeout(timeoutId);
    
    return {
      url,
      status: response.status,
      ok: response.ok,
      statusText: response.statusText
    };
  } catch (error) {
    return {
      url,
      status: null,
      ok: false,
      error: error.message
    };
  }
};

// Check all URLs in parallel with concurrency limit
const checkAllUrls = async (urls, concurrency = 5) => {
  const results = [];
  const errors = [];
  const warnings = [];
  
  console.log(`${colors.blue}🔍 Checking ${urls.length} URLs with concurrency limit of ${concurrency}...${colors.reset}\n`);
  
  // Process URLs in batches
  for (let i = 0; i < urls.length; i += concurrency) {
    const batch = urls.slice(i, i + concurrency);
    const batchPromises = batch.map(url => checkUrl(url));
    
    const batchResults = await Promise.all(batchPromises);
    
    // Process results
    batchResults.forEach(result => {
      results.push(result);
      
      if (result.ok) {
        console.log(`${colors.green}✅ ${result.status}${colors.reset} - ${result.url}`);
      } else if (result.status >= 300 && result.status < 400) {
        console.log(`${colors.yellow}⚠️  ${result.status}${colors.reset} - ${result.url} (Redirect)`);
        warnings.push(result);
      } else {
        console.log(`${colors.red}❌ ${result.status || 'ERROR'}${colors.reset} - ${result.url} ${result.error ? `(${result.error})` : ''}`);
        errors.push(result);
      }
    });
    
    // Add a small delay between batches to be respectful
    if (i + concurrency < urls.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return { results, errors, warnings };
};

// Generate detailed report
const generateReport = (results, errors, warnings) => {
  console.log(`\n${colors.bold}📊 SITEMAP CHECK REPORT${colors.reset}`);
  console.log('='.repeat(50));
  
  const total = results.length;
  const successful = results.filter(r => r.ok).length;
  const failed = errors.length;
  const redirects = warnings.length;
  
  console.log(`${colors.blue}Total URLs checked:${colors.reset} ${total}`);
  console.log(`${colors.green}Successful (2xx):${colors.reset} ${successful}`);
  console.log(`${colors.yellow}Redirects (3xx):${colors.reset} ${redirects}`);
  console.log(`${colors.red}Failed (4xx/5xx/Error):${colors.reset} ${failed}`);
  console.log(`${colors.blue}Success Rate:${colors.reset} ${((successful / total) * 100).toFixed(1)}%\n`);
  
  if (warnings.length > 0) {
    console.log(`${colors.yellow}⚠️  REDIRECTS (${warnings.length}):${colors.reset}`);
    warnings.forEach(result => {
      console.log(`   ${result.status} - ${result.url}`);
    });
    console.log('');
  }
  
  if (errors.length > 0) {
    console.log(`${colors.red}❌ ERRORS (${errors.length}):${colors.reset}`);
    errors.forEach(result => {
      console.log(`   ${result.status || 'ERROR'} - ${result.url} ${result.error ? `(${result.error})` : ''}`);
    });
    console.log('');
  }
  
  // Save detailed report to file
  const reportPath = path.join(__dirname, '../sitemap-check-report.json');
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total,
      successful,
      failed,
      redirects,
      successRate: ((successful / total) * 100).toFixed(1)
    },
    results: results.map(r => ({
      url: r.url,
      status: r.status,
      ok: r.ok,
      error: r.error || null
    }))
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2), 'utf8');
  console.log(`${colors.blue}📄 Detailed report saved to:${colors.reset} ${reportPath}`);
  
  return { successful, failed, redirects };
};

// Transform URLs based on command line options
const transformUrls = (urls, args) => {
  const localFlag = args.includes('--local');
  const spaMode = args.includes('--spa-mode');
  const baseUrlArg = args.find(arg => arg.startsWith('--base-url='));

  if (localFlag || spaMode) {
    const localBaseUrl = 'http://localhost:5173';
    return urls.map(url => {
      const urlObj = new URL(url);
      return `${localBaseUrl}${urlObj.pathname}`;
    });
  }

  if (baseUrlArg) {
    const customBaseUrl = baseUrlArg.replace('--base-url=', '');
    return urls.map(url => {
      const urlObj = new URL(url);
      return `${customBaseUrl}${urlObj.pathname}`;
    });
  }

  return urls;
};

// Main function
const main = async () => {
  console.log(`${colors.bold}🔍 SITEMAP URL CHECKER${colors.reset}`);
  console.log('='.repeat(30));

  const args = process.argv.slice(2);
  const sitemapPath = path.join(__dirname, '../public/sitemap.xml');

  if (!fs.existsSync(sitemapPath)) {
    console.error(`${colors.red}❌ Sitemap not found at: ${sitemapPath}${colors.reset}`);
    process.exit(1);
  }

  console.log(`${colors.blue}📍 Checking sitemap:${colors.reset} ${sitemapPath}`);

  const originalUrls = parseSitemap(sitemapPath);
  const urls = transformUrls(originalUrls, args);

  if (args.includes('--local') || args.includes('--spa-mode')) {
    console.log(`${colors.yellow}🔧 Running in local/SPA mode - checking against localhost:5173${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Make sure your dev server is running: npm run dev${colors.reset}`);
  }

  console.log('');

  const { results, errors, warnings } = await checkAllUrls(urls);
  const { successful, failed } = generateReport(results, errors, warnings);

  // Exit with error code if there are failures
  if (failed > 0) {
    console.log(`${colors.red}💥 Check completed with ${failed} errors!${colors.reset}`);
    process.exit(1);
  } else {
    console.log(`${colors.green}🎉 All URLs are accessible!${colors.reset}`);
    process.exit(0);
  }
};

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
${colors.bold}Sitemap URL Checker${colors.reset}

Usage: node scripts/check-sitemap.js [options]

Options:
  --help, -h          Show this help message
  --local             Check against local dev server (http://localhost:5173)
  --base-url=URL      Use custom base URL instead of sitemap URLs
  --spa-mode          Check SPA routes (replace domain with local server)

This script checks all URLs in public/sitemap.xml to ensure they are accessible.
It generates a detailed report and exits with code 1 if any URLs are not accessible.

Examples:
  npm run check-sitemap                    # Check production URLs
  npm run check-sitemap -- --local        # Check local dev server
  npm run check-sitemap -- --spa-mode     # Check SPA routes locally
`);
  process.exit(0);
}

// Run the checker
main().catch(error => {
  console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error);
  process.exit(1);
});
