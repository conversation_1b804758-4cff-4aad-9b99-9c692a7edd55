#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Configuration
const CONFIG = {
  host: 'cursivefontgenerator.top',
  indexNowEndpoint: 'https://api.indexnow.org/indexnow',
  bingEndpoint: 'https://www.bing.com/indexnow',
  googleEndpoint: 'https://www.google.com/indexnow',
  keyFile: 'indexnow-key.txt',
  maxUrlsPerRequest: 10000, // IndexNow limit
  retryAttempts: 3,
  retryDelay: 1000 // ms
};

// Generate or load IndexNow key
const getOrCreateIndexNowKey = () => {
  const keyPath = path.join(__dirname, '../public', CONFIG.keyFile);

  if (fs.existsSync(keyPath)) {
    const existingKey = fs.readFileSync(keyPath, 'utf8').trim();
    console.log(`${colors.blue}🔑 Using IndexNow key: ${existingKey}${colors.reset}`);

    // Also check if key-named file exists
    const keyNamedFilePath = path.join(__dirname, '../public', `${existingKey}.txt`);
    if (fs.existsSync(keyNamedFilePath)) {
      console.log(`${colors.green}✅ Key-named file exists: ${existingKey}.txt${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️  Creating key-named file: ${existingKey}.txt${colors.reset}`);
      fs.writeFileSync(keyNamedFilePath, existingKey, 'utf8');
    }

    return existingKey;
  }

  // Generate new key (32 characters, alphanumeric)
  const key = crypto.randomBytes(16).toString('hex');
  fs.writeFileSync(keyPath, key, 'utf8');

  // Also create key-named file
  const keyNamedFilePath = path.join(__dirname, '../public', `${key}.txt`);
  fs.writeFileSync(keyNamedFilePath, key, 'utf8');

  console.log(`${colors.green}🔑 Generated new IndexNow key: ${key}${colors.reset}`);
  console.log(`${colors.yellow}⚠️  Key saved to: ${keyPath}${colors.reset}`);
  console.log(`${colors.yellow}⚠️  Key-named file: ${keyNamedFilePath}${colors.reset}`);
  console.log(`${colors.yellow}⚠️  Make sure files are accessible at:${colors.reset}`);
  console.log(`${colors.yellow}     https://${CONFIG.host}/${CONFIG.keyFile}${colors.reset}`);
  console.log(`${colors.yellow}     https://${CONFIG.host}/${key}.txt${colors.reset}`);

  return key;
};

// Parse sitemap.xml to extract URLs
const parseSitemap = (sitemapPath) => {
  try {
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
    
    if (!urlMatches) {
      throw new Error('No URLs found in sitemap.xml');
    }
    
    return urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
  } catch (error) {
    console.error(`${colors.red}❌ Error reading sitemap.xml:${colors.reset}`, error.message);
    process.exit(1);
  }
};

// Submit URLs to IndexNow with multiple endpoints
const submitToIndexNow = async (urls, key, attempt = 1) => {
  const payload = {
    host: CONFIG.host,
    key: key,
    keyLocation: `https://${CONFIG.host}/${CONFIG.keyFile}`,
    urlList: urls
  };

  // Try different endpoints
  const endpoints = [
    { name: 'IndexNow API', url: CONFIG.indexNowEndpoint },
    { name: 'Bing Direct', url: CONFIG.bingEndpoint }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`${colors.blue}📤 Submitting ${urls.length} URLs to ${endpoint.name} (attempt ${attempt})...${colors.reset}`);

      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'CursiveFontGenerator-IndexNow/1.0'
        },
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();

      if (response.ok) {
        console.log(`${colors.green}✅ Successfully submitted to ${endpoint.name}!${colors.reset}`);
        console.log(`${colors.blue}📊 Status: ${response.status} ${response.statusText}${colors.reset}`);
        return { success: true, status: response.status, response: responseText, endpoint: endpoint.name };
      } else {
        console.log(`${colors.yellow}⚠️  ${endpoint.name} failed: HTTP ${response.status}${colors.reset}`);
        if (responseText) {
          console.log(`${colors.yellow}   Response: ${responseText}${colors.reset}`);
        }
      }
    } catch (error) {
      console.log(`${colors.yellow}⚠️  ${endpoint.name} error: ${error.message}${colors.reset}`);
    }
  }

  // If all endpoints failed, retry
  if (attempt < CONFIG.retryAttempts) {
    console.log(`${colors.yellow}🔄 All endpoints failed, retrying in ${CONFIG.retryDelay}ms...${colors.reset}`);
    await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay));
    return submitToIndexNow(urls, key, attempt + 1);
  }

  return { success: false, error: 'All endpoints failed' };
};

// Submit URLs in batches if needed
const submitUrlsBatched = async (urls, key) => {
  if (urls.length <= CONFIG.maxUrlsPerRequest) {
    return await submitToIndexNow(urls, key);
  }
  
  console.log(`${colors.yellow}⚠️  Large URL list (${urls.length}), splitting into batches...${colors.reset}`);
  
  const results = [];
  for (let i = 0; i < urls.length; i += CONFIG.maxUrlsPerRequest) {
    const batch = urls.slice(i, i + CONFIG.maxUrlsPerRequest);
    console.log(`${colors.blue}📦 Processing batch ${Math.floor(i / CONFIG.maxUrlsPerRequest) + 1}/${Math.ceil(urls.length / CONFIG.maxUrlsPerRequest)}${colors.reset}`);
    
    const result = await submitToIndexNow(batch, key);
    results.push(result);
    
    // Add delay between batches
    if (i + CONFIG.maxUrlsPerRequest < urls.length) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  return results;
};

// Verify key file is accessible
const verifyKeyFile = async (key) => {
  const keyUrl = `https://${CONFIG.host}/${CONFIG.keyFile}`;
  
  try {
    console.log(`${colors.blue}🔍 Verifying key file accessibility...${colors.reset}`);
    const response = await fetch(keyUrl);
    const content = await response.text();
    
    if (response.ok && content.trim() === key) {
      console.log(`${colors.green}✅ Key file is accessible and valid${colors.reset}`);
      return true;
    } else {
      console.log(`${colors.red}❌ Key file verification failed${colors.reset}`);
      console.log(`${colors.yellow}   Expected: ${key}${colors.reset}`);
      console.log(`${colors.yellow}   Got: ${content.trim()}${colors.reset}`);
      return false;
    }
  } catch (error) {
    console.log(`${colors.red}❌ Key file not accessible: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Make sure ${keyUrl} is accessible${colors.reset}`);
    return false;
  }
};

// Save submission log
const saveSubmissionLog = (urls, results, key) => {
  const logPath = path.join(__dirname, '../indexnow-submissions.json');
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    host: CONFIG.host,
    key: key,
    urlCount: urls.length,
    results: Array.isArray(results) ? results : [results],
    urls: urls
  };
  
  let existingLogs = [];
  if (fs.existsSync(logPath)) {
    try {
      existingLogs = JSON.parse(fs.readFileSync(logPath, 'utf8'));
    } catch (error) {
      console.log(`${colors.yellow}⚠️  Could not read existing log file${colors.reset}`);
    }
  }
  
  existingLogs.push(logEntry);
  
  // Keep only last 50 submissions
  if (existingLogs.length > 50) {
    existingLogs = existingLogs.slice(-50);
  }
  
  fs.writeFileSync(logPath, JSON.stringify(existingLogs, null, 2), 'utf8');
  console.log(`${colors.blue}📄 Submission logged to: ${logPath}${colors.reset}`);
};

// Main function
const main = async () => {
  console.log(`${colors.bold}🚀 BING INDEXNOW SUBMITTER${colors.reset}`);
  console.log('='.repeat(35));
  
  const args = process.argv.slice(2);
  const skipVerification = args.includes('--skip-verification');
  const urlsArg = args.find(arg => arg.startsWith('--urls='));
  
  // Get IndexNow key
  const key = getOrCreateIndexNowKey();
  
  // Verify key file accessibility (unless skipped)
  if (!skipVerification) {
    const keyAccessible = await verifyKeyFile(key);
    if (!keyAccessible) {
      console.log(`${colors.yellow}⚠️  Key file verification failed. Use --skip-verification to proceed anyway.${colors.reset}`);
      process.exit(1);
    }
  }
  
  // Get URLs to submit
  let urls;
  if (urlsArg) {
    // Submit specific URLs
    const urlList = urlsArg.replace('--urls=', '').split(',');
    urls = urlList.map(url => url.trim()).filter(url => url);
    console.log(`${colors.blue}📋 Submitting ${urls.length} specific URLs${colors.reset}`);
  } else {
    // Submit all URLs from sitemap
    const sitemapPath = path.join(__dirname, '../public/sitemap.xml');
    if (!fs.existsSync(sitemapPath)) {
      console.error(`${colors.red}❌ Sitemap not found at: ${sitemapPath}${colors.reset}`);
      process.exit(1);
    }
    
    urls = parseSitemap(sitemapPath);
    console.log(`${colors.blue}📋 Submitting all ${urls.length} URLs from sitemap${colors.reset}`);
  }
  
  if (urls.length === 0) {
    console.log(`${colors.yellow}⚠️  No URLs to submit${colors.reset}`);
    process.exit(0);
  }
  
  // Submit URLs
  const results = await submitUrlsBatched(urls, key);
  
  // Save log
  saveSubmissionLog(urls, results, key);
  
  // Check results
  const allSuccessful = Array.isArray(results) 
    ? results.every(r => r.success)
    : results.success;
  
  if (allSuccessful) {
    console.log(`${colors.green}🎉 All submissions completed successfully!${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`${colors.red}💥 Some submissions failed. Check the logs for details.${colors.reset}`);
    process.exit(1);
  }
};

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
${colors.bold}Bing IndexNow Submitter${colors.reset}

Usage: node scripts/bing-indexnow.js [options]

Options:
  --help, -h              Show this help message
  --skip-verification     Skip key file accessibility verification
  --urls=url1,url2,...    Submit specific URLs instead of all sitemap URLs

Examples:
  node scripts/bing-indexnow.js
  node scripts/bing-indexnow.js --skip-verification
  node scripts/bing-indexnow.js --urls=https://example.com,https://example.com/page

This script submits URLs to Bing's IndexNow API for faster indexing.
It automatically generates an IndexNow key if one doesn't exist.
`);
  process.exit(0);
}

// Run the submitter
main().catch(error => {
  console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error);
  process.exit(1);
});
