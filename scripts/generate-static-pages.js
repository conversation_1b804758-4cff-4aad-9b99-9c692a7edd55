#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Route configurations with SEO metadata
const routes = [
  {
    path: '/',
    title: 'Cursive Font Generator | Copy Paste Script & Tattoo Fonts',
    description: 'Generate beautiful cursive fonts for Instagram, tattoos, and social media. Copy and paste fancy text styles instantly. Free online cursive font generator.',
    keywords: 'cursive font generator, fancy text, script fonts, tattoo fonts, Instagram fonts'
  },
  {
    path: '/about',
    title: 'About Us | Cursive Font Generator',
    description: 'Learn about our mission to provide the best cursive font generation tools for social media, tattoos, and creative projects.',
    keywords: 'about cursive fonts, font generator team, typography tools'
  },
  {
    path: '/contact',
    title: 'Contact Us | Cursive Font Generator',
    description: 'Get in touch with our team for support, feedback, or collaboration opportunities.',
    keywords: 'contact, support, feedback, cursive fonts'
  },
  {
    path: '/privacy',
    title: 'Privacy Policy | Cursive Font Generator',
    description: 'Our privacy policy explains how we handle your data when using our cursive font generator.',
    keywords: 'privacy policy, data protection, user privacy'
  },
  {
    path: '/terms',
    title: 'Terms of Service | Cursive Font Generator',
    description: 'Terms and conditions for using our cursive font generator and related services.',
    keywords: 'terms of service, usage terms, legal'
  },
  // Font pages
  {
    path: '/cursive-fonts',
    title: 'Cursive Fonts Generator | Elegant Script Styles',
    description: 'Generate beautiful cursive fonts perfect for elegant designs, invitations, and social media posts. Copy and paste instantly.',
    keywords: 'cursive fonts, elegant fonts, script fonts, handwriting fonts'
  },
  {
    path: '/elegant-cursive-fonts',
    title: 'Elegant Cursive Fonts | Sophisticated Script Styles',
    description: 'Create sophisticated elegant cursive fonts for luxury brands, wedding invitations, and premium designs.',
    keywords: 'elegant cursive fonts, luxury fonts, sophisticated scripts, wedding fonts'
  },
  {
    path: '/modern-cursive-fonts',
    title: 'Modern Cursive Fonts | Contemporary Script Styles',
    description: 'Modern cursive fonts with contemporary flair. Perfect for trendy designs and social media content.',
    keywords: 'modern cursive fonts, contemporary scripts, trendy fonts, modern typography'
  },
  {
    path: '/vintage-cursive-fonts',
    title: 'Vintage Cursive Fonts | Retro Script Styles',
    description: 'Classic vintage cursive fonts with retro charm. Ideal for nostalgic designs and vintage branding.',
    keywords: 'vintage cursive fonts, retro fonts, classic scripts, nostalgic typography'
  },
  {
    path: '/romantic-cursive-fonts',
    title: 'Romantic Cursive Fonts | Love & Wedding Scripts',
    description: 'Romantic cursive fonts perfect for love letters, wedding invitations, and romantic designs.',
    keywords: 'romantic cursive fonts, love fonts, wedding scripts, romantic typography'
  },
  {
    path: '/script-fonts',
    title: 'Script Fonts Generator | Handwritten Styles',
    description: 'Generate beautiful script fonts that mimic handwritten text. Perfect for personal and professional use.',
    keywords: 'script fonts, handwritten fonts, calligraphy fonts, cursive scripts'
  },
  {
    path: '/elegant-script-fonts',
    title: 'Elegant Script Fonts | Premium Handwriting Styles',
    description: 'Elegant script fonts with premium handwriting styles for sophisticated designs and branding.',
    keywords: 'elegant script fonts, premium fonts, sophisticated handwriting, luxury scripts'
  },
  {
    path: '/handwriting-fonts',
    title: 'Handwriting Fonts Generator | Natural Script Styles',
    description: 'Create natural handwriting fonts that look authentic and personal. Perfect for signatures and notes.',
    keywords: 'handwriting fonts, natural scripts, signature fonts, personal fonts'
  },
  {
    path: '/calligraphy-fonts',
    title: 'Calligraphy Fonts Generator | Artistic Script Styles',
    description: 'Beautiful calligraphy fonts for artistic projects, invitations, and decorative text.',
    keywords: 'calligraphy fonts, artistic fonts, decorative scripts, ornamental fonts'
  },
  {
    path: '/tattoo-fonts',
    title: 'Tattoo Fonts Generator | Script & Gothic Styles',
    description: 'Design tattoo fonts with script and gothic styles. Perfect for tattoo artists and body art enthusiasts.',
    keywords: 'tattoo fonts, gothic fonts, tattoo scripts, body art fonts'
  },
  {
    path: '/gothic-fonts',
    title: 'Gothic Fonts Generator | Medieval & Dark Styles',
    description: 'Gothic fonts with medieval and dark aesthetics. Ideal for dramatic designs and gothic themes.',
    keywords: 'gothic fonts, medieval fonts, dark fonts, blackletter fonts'
  },
  {
    path: '/old-english-fonts',
    title: 'Old English Fonts Generator | Traditional Gothic Styles',
    description: 'Traditional Old English fonts with classic gothic styling for formal and ceremonial use.',
    keywords: 'old english fonts, traditional gothic, formal fonts, ceremonial fonts'
  },
  {
    path: '/tattoo-script-fonts',
    title: 'Tattoo Script Fonts | Elegant Body Art Typography',
    description: 'Elegant script fonts specifically designed for tattoo art and body decoration.',
    keywords: 'tattoo script fonts, body art typography, tattoo calligraphy, ink fonts'
  },
  {
    path: '/bold-fonts',
    title: 'Bold Fonts Generator | Strong Typography Styles',
    description: 'Generate bold fonts for impactful headlines and attention-grabbing text.',
    keywords: 'bold fonts, strong typography, heavy fonts, impact fonts'
  },
  {
    path: '/italic-fonts',
    title: 'Italic Fonts Generator | Slanted Text Styles',
    description: 'Create italic fonts with elegant slanted styling for emphasis and sophistication.',
    keywords: 'italic fonts, slanted fonts, oblique fonts, emphasized text'
  },
  {
    path: '/small-caps-fonts',
    title: 'Small Caps Fonts Generator | Professional Typography',
    description: 'Small caps fonts for professional documents and sophisticated typography.',
    keywords: 'small caps fonts, professional typography, formal fonts, business fonts'
  },
  {
    path: '/vaporwave-fonts',
    title: 'Vaporwave Fonts Generator | Retro Aesthetic Styles',
    description: 'Vaporwave fonts with retro aesthetic styling perfect for 80s and synthwave designs.',
    keywords: 'vaporwave fonts, retro aesthetic, 80s fonts, synthwave typography'
  }
];

// Generate HTML template
const generateHTML = (route) => {
  const baseUrl = 'https://cursivefontgenerator.top';
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>${route.title}</title>
  <meta name="description" content="${route.description}" />
  <meta name="keywords" content="${route.keywords}" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="${baseUrl}${route.path}" />
  <meta property="og:title" content="${route.title}" />
  <meta property="og:description" content="${route.description}" />
  <meta property="og:image" content="${baseUrl}/og-image.png" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="${baseUrl}${route.path}" />
  <meta property="twitter:title" content="${route.title}" />
  <meta property="twitter:description" content="${route.description}" />
  <meta property="twitter:image" content="${baseUrl}/og-image.png" />
  
  <!-- Canonical URL -->
  <link rel="canonical" href="${baseUrl}${route.path}" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/logo.svg" />
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/assets/index.css" as="style" />
  <link rel="preload" href="/assets/index.js" as="script" />
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/assets/index.css" />
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/assets/index.js"></script>
  
  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Cursive Font Generator",
    "url": "${baseUrl}",
    "description": "Generate beautiful cursive fonts for social media, tattoos, and creative projects",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    }
  }
  </script>
</body>
</html>`;
};

// Main function to generate static pages
const generateStaticPages = () => {
  console.log(`${colors.bold}🏗️  STATIC PAGE GENERATOR${colors.reset}`);
  console.log('='.repeat(35));
  
  const distPath = path.join(__dirname, '../dist');
  
  if (!fs.existsSync(distPath)) {
    console.error(`${colors.red}❌ Dist folder not found. Please run 'npm run build' first.${colors.reset}`);
    process.exit(1);
  }
  
  let generated = 0;
  let errors = 0;
  
  routes.forEach(route => {
    try {
      const html = generateHTML(route);
      
      // Create directory structure if needed
      const routePath = route.path === '/' ? '/index' : route.path;
      const filePath = path.join(distPath, `${routePath}.html`);
      const dirPath = path.dirname(filePath);
      
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      
      // Write HTML file
      fs.writeFileSync(filePath, html, 'utf8');
      
      console.log(`${colors.green}✅${colors.reset} Generated: ${route.path} -> ${path.relative(distPath, filePath)}`);
      generated++;
      
    } catch (error) {
      console.error(`${colors.red}❌${colors.reset} Failed to generate ${route.path}:`, error.message);
      errors++;
    }
  });
  
  console.log(`\n${colors.bold}📊 GENERATION SUMMARY${colors.reset}`);
  console.log('='.repeat(25));
  console.log(`${colors.green}✅ Generated:${colors.reset} ${generated} pages`);
  console.log(`${colors.red}❌ Errors:${colors.reset} ${errors} pages`);
  console.log(`${colors.blue}📁 Output:${colors.reset} ${distPath}`);
  
  if (errors > 0) {
    console.log(`${colors.red}💥 Generation completed with errors!${colors.reset}`);
    process.exit(1);
  } else {
    console.log(`${colors.green}🎉 All pages generated successfully!${colors.reset}`);
    process.exit(0);
  }
};

// Run the generator
generateStaticPages();
