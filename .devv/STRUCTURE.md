# This file is only for editing file nodes, do not break the structure

/src
├── assets/          # Static resources directory, storing static files like images and fonts
│
├── components/      # Components directory
│   ├── ui/         # Pre-installed shadcn/ui components, avoid modifying or rewriting unless necessary
│   ├── Header.tsx  # Website header component with navigation menu
│   ├── HeroSection.tsx # Main hero section with quick text transformation
│   ├── FontGenerator.tsx # Core font generator component with text transformation functionality
│   ├── PreviewSection.tsx # Visual preview component showing text in different platforms
│   ├── FeaturesSection.tsx # Features highlight section showcasing tool benefits
│   ├── WhatIsSection.tsx # Educational section explaining font generator concept
│   ├── SymbolLibrary.tsx # Symbol library component with categorized symbols for copying
│   ├── ContentHub.tsx # Usage guide and FAQ component with accordion sections
│   └── Footer.tsx  # Website footer component with navigation and links
│
├── hooks/          # Custom Hooks directory
│   ├── use-mobile.ts # Pre-installed mobile detection Hook from shadcn (import { useIsMobile } from '@/hooks/use-mobile')
│   └── use-toast.ts  # Toast notification system hook for displaying toast messages (import { useToast } from '@/hooks/use-toast')
│
├── lib/            # Utility library directory
│   └── utils.ts    # Utility functions, including the cn function for merging Tailwind class names
│
├── pages/          # Page components directory, based on React Router structure
│   ├── HomePage.tsx # Home page component, serving as the main entry point of the application
│   └── NotFoundPage.tsx # 404 error page component, displays when users access non-existent routes
│
├── App.tsx         # Root component, with React Router routing system configured
│                   # Add new route configurations in this file
│                   # Includes catch-all route (*) for 404 page handling
│
├── main.tsx        # Entry file, rendering the root component and mounting to the DOM
│
├── index.css       # Global styles file, containing Tailwind configuration and custom styles
│                   # Modify theme colors and design system variables in this file 
│
└── tailwind.config.js  # Tailwind CSS v3 configuration file
                      # Contains theme customization, plugins, and content paths
                      # Includes shadcn/ui theme configuration 



